"""
Painel de Configurações
Widget para configurações gerais do aplicativo
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from pathlib import Path
from typing import Dict, Any

class SettingsPanel(ttk.Frame):
    """Painel de configurações do aplicativo"""

    def __init__(self, parent, config_manager):
        super().__init__(parent)

        self.config_manager = config_manager

        # Variáveis de configuração
        self.setup_variables()

        # Criar interface com scroll
        self.create_scrollable_frame()
        self.create_widgets()
        self.setup_layout()
        self.bind_events()

        # Carregar configurações atuais
        self.load_current_settings()

    def create_scrollable_frame(self):
        """Criar frame com scroll"""

        # Canvas e scrollbar
        self.canvas = tk.Canvas(self, highlightthickness=0)
        self.scrollbar = ttk.Scrollbar(self, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)

        # Configurar scroll
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)

        # Layout do canvas
        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")

        # Bind mousewheel
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)

    def _on_mousewheel(self, event):
        """Scroll com mouse wheel"""
        self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
    def setup_variables(self):
        """Configurar variáveis de interface"""
        
        # Configurações gerais
        self.output_directory = tk.StringVar()
        self.open_output_folder = tk.BooleanVar()
        self.clean_before_build = tk.BooleanVar()
        self.backup_original = tk.BooleanVar()
        
        # Configurações de interface
        self.theme = tk.StringVar()
        self.window_size = tk.StringVar()
        self.remember_window_position = tk.BooleanVar()
        self.show_advanced_options = tk.BooleanVar()
        
        # Configurações de logging
        self.log_level = tk.StringVar()
        self.max_log_files = tk.IntVar()
        
    def create_widgets(self):
        """Criar widgets do painel"""

        # Usar scrollable_frame como parent
        parent = self.scrollable_frame

        # === SEÇÃO GERAL ===
        self.general_frame = ttk.LabelFrame(parent, text="Configurações Gerais", padding="10")
        
        # Diretório de saída padrão
        ttk.Label(self.general_frame, text="Diretório de saída padrão:").grid(
            row=0, column=0, sticky="w", pady=(0, 5)
        )
        
        output_frame = ttk.Frame(self.general_frame)
        output_frame.grid(row=1, column=0, sticky="ew", pady=(0, 10))
        output_frame.columnconfigure(0, weight=1)
        
        self.output_entry = ttk.Entry(output_frame, textvariable=self.output_directory, width=50)
        self.output_entry.grid(row=0, column=0, sticky="ew", padx=(0, 5))
        
        self.output_browse_btn = ttk.Button(
            output_frame, text="Procurar", command=self.browse_output_directory
        )
        self.output_browse_btn.grid(row=0, column=1)
        
        # Opções de build
        ttk.Label(self.general_frame, text="Opções de Build:").grid(
            row=2, column=0, sticky="w", pady=(10, 5)
        )
        
        ttk.Checkbutton(
            self.general_frame,
            text="Abrir pasta de saída após build",
            variable=self.open_output_folder
        ).grid(row=3, column=0, sticky="w", pady=2)
        
        ttk.Checkbutton(
            self.general_frame,
            text="Limpar arquivos temporários antes do build",
            variable=self.clean_before_build
        ).grid(row=4, column=0, sticky="w", pady=2)
        
        ttk.Checkbutton(
            self.general_frame,
            text="Fazer backup do projeto original",
            variable=self.backup_original
        ).grid(row=5, column=0, sticky="w", pady=2)
        
        # === SEÇÃO INTERFACE ===
        self.ui_frame = ttk.LabelFrame(parent, text="Interface do Usuário", padding="10")
        
        # Tema
        ttk.Label(self.ui_frame, text="Tema:").grid(row=0, column=0, sticky="w", pady=(0, 5))
        
        theme_combo = ttk.Combobox(
            self.ui_frame,
            textvariable=self.theme,
            values=["default", "clam", "alt", "classic"],
            state="readonly",
            width=20
        )
        theme_combo.grid(row=1, column=0, sticky="w", pady=(0, 10))
        
        # Tamanho da janela
        ttk.Label(self.ui_frame, text="Tamanho da janela:").grid(row=2, column=0, sticky="w", pady=(0, 5))
        
        size_combo = ttk.Combobox(
            self.ui_frame,
            textvariable=self.window_size,
            values=["800x600", "1024x768", "1200x800", "1400x900"],
            state="readonly",
            width=20
        )
        size_combo.grid(row=3, column=0, sticky="w", pady=(0, 10))
        
        # Opções de interface
        ttk.Checkbutton(
            self.ui_frame,
            text="Lembrar posição da janela",
            variable=self.remember_window_position
        ).grid(row=4, column=0, sticky="w", pady=2)
        
        ttk.Checkbutton(
            self.ui_frame,
            text="Mostrar opções avançadas",
            variable=self.show_advanced_options
        ).grid(row=5, column=0, sticky="w", pady=2)
        
        # === SEÇÃO LOGGING ===
        self.log_frame = ttk.LabelFrame(parent, text="Configurações de Log", padding="10")
        
        # Nível de log
        ttk.Label(self.log_frame, text="Nível de log:").grid(row=0, column=0, sticky="w", pady=(0, 5))
        
        log_combo = ttk.Combobox(
            self.log_frame,
            textvariable=self.log_level,
            values=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
            state="readonly",
            width=20
        )
        log_combo.grid(row=1, column=0, sticky="w", pady=(0, 10))
        
        # Máximo de arquivos de log
        ttk.Label(self.log_frame, text="Máximo de arquivos de log:").grid(row=2, column=0, sticky="w", pady=(0, 5))
        
        log_spin = ttk.Spinbox(
            self.log_frame,
            from_=1,
            to=30,
            textvariable=self.max_log_files,
            width=10
        )
        log_spin.grid(row=3, column=0, sticky="w", pady=(0, 10))
        
        # === BOTÕES DE AÇÃO ===
        self.buttons_frame = ttk.Frame(parent)
        
        self.save_btn = ttk.Button(
            self.buttons_frame,
            text="Salvar Configurações",
            command=self.save_settings,
            style="Accent.TButton"
        )
        self.save_btn.pack(side="left", padx=(0, 10))
        
        self.reset_btn = ttk.Button(
            self.buttons_frame,
            text="Restaurar Padrões",
            command=self.reset_to_defaults
        )
        self.reset_btn.pack(side="left", padx=(0, 10))
        
        self.open_config_btn = ttk.Button(
            self.buttons_frame,
            text="Abrir Pasta de Configurações",
            command=self.open_config_folder
        )
        self.open_config_btn.pack(side="right")
        
    def setup_layout(self):
        """Configurar layout do painel"""

        # Configurar redimensionamento do frame scrollable
        self.scrollable_frame.columnconfigure(0, weight=1)
        
        # Posicionar seções
        self.general_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        self.general_frame.columnconfigure(0, weight=1)
        
        self.ui_frame.grid(row=1, column=0, sticky="ew", pady=(0, 10))
        self.ui_frame.columnconfigure(0, weight=1)
        
        self.log_frame.grid(row=2, column=0, sticky="ew", pady=(0, 10))
        self.log_frame.columnconfigure(0, weight=1)
        
        self.buttons_frame.grid(row=3, column=0, sticky="ew", pady=(10, 0))
        
    def bind_events(self):
        """Vincular eventos"""
        
        # Auto-salvar quando valores mudam
        self.output_directory.trace_add("write", self.on_setting_changed)
        self.theme.trace_add("write", self.on_theme_changed)
        
    def load_current_settings(self):
        """Carregar configurações atuais"""
        
        # Configurações gerais
        self.output_directory.set(self.config_manager.get('output_directory', ''))
        self.open_output_folder.set(self.config_manager.get('build_settings.open_output_folder', True))
        self.clean_before_build.set(self.config_manager.get('build_settings.clean_before_build', True))
        self.backup_original.set(self.config_manager.get('build_settings.backup_original', False))
        
        # Configurações de interface
        self.theme.set(self.config_manager.get('ui_settings.theme', 'default'))
        self.window_size.set(self.config_manager.get('ui_settings.window_size', '800x600'))
        self.remember_window_position.set(self.config_manager.get('ui_settings.remember_window_position', True))
        self.show_advanced_options.set(self.config_manager.get('ui_settings.show_advanced_options', False))
        
        # Configurações de logging
        self.log_level.set(self.config_manager.get('logging.level', 'INFO'))
        self.max_log_files.set(self.config_manager.get('logging.max_files', 7))
        
    def save_settings(self):
        """Salvar todas as configurações"""
        
        try:
            # Configurações gerais
            self.config_manager.set('output_directory', self.output_directory.get())
            self.config_manager.set('build_settings.open_output_folder', self.open_output_folder.get())
            self.config_manager.set('build_settings.clean_before_build', self.clean_before_build.get())
            self.config_manager.set('build_settings.backup_original', self.backup_original.get())
            
            # Configurações de interface
            self.config_manager.set('ui_settings.theme', self.theme.get())
            self.config_manager.set('ui_settings.window_size', self.window_size.get())
            self.config_manager.set('ui_settings.remember_window_position', self.remember_window_position.get())
            self.config_manager.set('ui_settings.show_advanced_options', self.show_advanced_options.get())
            
            # Configurações de logging
            self.config_manager.set('logging.level', self.log_level.get())
            self.config_manager.set('logging.max_files', self.max_log_files.get())
            
            messagebox.showinfo("Sucesso", "Configurações salvas com sucesso!")
            
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao salvar configurações: {e}")
            
    def reset_to_defaults(self):
        """Restaurar configurações padrão"""
        
        if messagebox.askyesno("Confirmar", "Restaurar todas as configurações para os valores padrão?"):
            self.config_manager.reset_to_defaults()
            self.load_current_settings()
            messagebox.showinfo("Sucesso", "Configurações restauradas para os padrões!")
            
    def browse_output_directory(self):
        """Procurar diretório de saída"""
        
        directory = filedialog.askdirectory(
            title="Selecionar Diretório de Saída Padrão",
            initialdir=self.output_directory.get()
        )
        
        if directory:
            self.output_directory.set(directory)
            
    def open_config_folder(self):
        """Abrir pasta de configurações"""
        
        config_path = self.config_manager.config_dir
        if config_path.exists():
            import os
            os.startfile(str(config_path))
        else:
            messagebox.showwarning("Aviso", "Pasta de configurações não encontrada")
            
    def on_setting_changed(self, *args):
        """Callback quando configuração muda"""
        # Auto-salvar configurações importantes
        pass
        
    def on_theme_changed(self, *args):
        """Callback quando tema muda"""
        new_theme = self.theme.get()
        if new_theme:
            try:
                style = ttk.Style()
                style.theme_use(new_theme)
            except Exception:
                pass  # Tema pode não estar disponível
