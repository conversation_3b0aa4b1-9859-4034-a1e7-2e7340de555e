"""
Componente Visualizador de Logs
Widget para exibir logs do aplicativo
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
from datetime import datetime
from typing import List, Dict

class LogViewer(ttk.Frame):
    """Widget para visualizar logs"""
    
    def __init__(self, parent, log_capture):
        super().__init__(parent)
        
        self.log_capture = log_capture
        
        self.create_widgets()
        self.setup_layout()
        self.bind_events()
        
        # Atualizar logs periodicamente
        self.update_logs()
        
    def create_widgets(self):
        """Criar widgets do componente"""
        
        # Frame de controles
        self.controls_frame = ttk.Frame(self)
        
        # Botões de controle
        self.clear_btn = ttk.Button(
            self.controls_frame,
            text="Limpar",
            command=self.clear_logs
        )
        
        self.refresh_btn = ttk.Button(
            self.controls_frame,
            text="Atualizar",
            command=self.refresh_logs
        )
        
        self.auto_scroll_var = tk.BooleanVar(value=True)
        self.auto_scroll_check = ttk.Checkbutton(
            self.controls_frame,
            text="Auto-scroll",
            variable=self.auto_scroll_var
        )
        
        # Filtro de nível
        self.level_label = ttk.Label(self.controls_frame, text="Nível:")
        
        self.level_var = tk.StringVar(value="INFO")
        self.level_combo = ttk.Combobox(
            self.controls_frame,
            textvariable=self.level_var,
            values=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
            state="readonly",
            width=10
        )
        
        # Área de texto para logs
        self.log_text = scrolledtext.ScrolledText(
            self,
            wrap=tk.WORD,
            height=15,
            font=("Consolas", 9)
        )
        
        # Configurar tags de cores para diferentes níveis
        self.setup_text_tags()
        
    def setup_layout(self):
        """Configurar layout"""
        
        self.columnconfigure(0, weight=1)
        self.rowconfigure(1, weight=1)
        
        # Frame de controles
        self.controls_frame.grid(row=0, column=0, sticky="ew", pady=(0, 5))
        
        self.clear_btn.pack(side="left", padx=(0, 5))
        self.refresh_btn.pack(side="left", padx=(0, 10))
        self.auto_scroll_check.pack(side="left", padx=(0, 10))
        
        self.level_label.pack(side="left", padx=(0, 5))
        self.level_combo.pack(side="left")
        
        # Área de texto
        self.log_text.grid(row=1, column=0, sticky="nsew")
        
    def bind_events(self):
        """Vincular eventos"""
        
        self.level_var.trace_add("write", self._on_level_changed)
        
    def setup_text_tags(self):
        """Configurar tags de formatação para o texto"""
        
        # Tags para diferentes níveis de log
        self.log_text.tag_config("DEBUG", foreground="gray")
        self.log_text.tag_config("INFO", foreground="black")
        self.log_text.tag_config("WARNING", foreground="orange")
        self.log_text.tag_config("ERROR", foreground="red")
        self.log_text.tag_config("CRITICAL", foreground="red", background="yellow")
        
        # Tag para timestamp
        self.log_text.tag_config("timestamp", foreground="blue")
        
    def update_logs(self):
        """Atualizar logs periodicamente"""
        
        self.refresh_logs()
        
        # Agendar próxima atualização
        self.after(1000, self.update_logs)
        
    def refresh_logs(self):
        """Atualizar exibição de logs"""
        
        # Obter logs
        logs = self.log_capture.get_logs()
        
        # Filtrar por nível
        min_level = self.level_var.get()
        level_order = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        min_level_index = level_order.index(min_level) if min_level in level_order else 1
        
        filtered_logs = [
            log for log in logs
            if log['level'] in level_order[min_level_index:]
        ]
        
        # Limpar texto atual
        self.log_text.delete(1.0, tk.END)
        
        # Adicionar logs filtrados
        for log in filtered_logs:
            self._add_log_entry(log)
            
        # Auto-scroll se habilitado
        if self.auto_scroll_var.get():
            self.log_text.see(tk.END)
            
    def _add_log_entry(self, log: Dict):
        """Adicionar entrada de log ao texto"""
        
        # Formatar timestamp
        timestamp = log['timestamp'].strftime("%H:%M:%S")
        
        # Inserir timestamp
        self.log_text.insert(tk.END, f"[{timestamp}] ", "timestamp")
        
        # Inserir nível e mensagem
        level = log['level']
        message = log['message']
        
        self.log_text.insert(tk.END, f"{level}: {message}\n", level)
        
    def clear_logs(self):
        """Limpar logs"""
        
        self.log_capture.clear_logs()
        self.log_text.delete(1.0, tk.END)
        
    def _on_level_changed(self, *args):
        """Callback quando nível de filtro muda"""
        
        self.refresh_logs()
