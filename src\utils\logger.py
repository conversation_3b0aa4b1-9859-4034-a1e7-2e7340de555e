"""
Sistema de Logging
Configuração e utilitários para logging do aplicativo
"""

import logging
import os
from pathlib import Path
from datetime import datetime
import colorlog

def setup_logger(name: str = 'gerador_executavel', level: int = logging.INFO) -> logging.Logger:
    """
    Configurar sistema de logging
    
    Args:
        name: Nome do logger
        level: Nível de logging
        
    Returns:
        Logger configurado
    """
    
    # Criar diretório de logs
    log_dir = Path.home() / '.gerador_executavel' / 'logs'
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Nome do arquivo de log com timestamp
    log_file = log_dir / f'app_{datetime.now().strftime("%Y%m%d")}.log'
    
    # Configurar logger
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # Evitar duplicação de handlers
    if logger.handlers:
        return logger
    
    # Formatter para arquivo
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Formatter colorido para console
    console_formatter = colorlog.ColoredFormatter(
        '%(log_color)s%(asctime)s - %(levelname)s - %(message)s%(reset)s',
        datefmt='%H:%M:%S',
        log_colors={
            'DEBUG': 'cyan',
            'INFO': 'green',
            'WARNING': 'yellow',
            'ERROR': 'red',
            'CRITICAL': 'red,bg_white',
        }
    )
    
    # Handler para arquivo
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(file_formatter)
    
    # Handler para console
    console_handler = colorlog.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(console_formatter)
    
    # Adicionar handlers
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

class LogCapture:
    """Capturar logs para exibição na GUI"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.logs = []
        self.max_logs = 1000
        
        # Criar handler personalizado
        self.handler = LogCaptureHandler(self)
        self.handler.setLevel(logging.INFO)
        
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
        self.handler.setFormatter(formatter)
        
        # Adicionar ao logger
        self.logger.addHandler(self.handler)
        
    def get_logs(self) -> list:
        """Obter lista de logs capturados"""
        return self.logs.copy()
        
    def clear_logs(self):
        """Limpar logs capturados"""
        self.logs.clear()
        
    def add_log(self, record: logging.LogRecord):
        """Adicionar log à lista"""
        formatted = self.handler.format(record)
        self.logs.append({
            'timestamp': datetime.fromtimestamp(record.created),
            'level': record.levelname,
            'message': record.getMessage(),
            'formatted': formatted
        })
        
        # Manter apenas os últimos logs
        if len(self.logs) > self.max_logs:
            self.logs = self.logs[-self.max_logs:]

class LogCaptureHandler(logging.Handler):
    """Handler personalizado para capturar logs"""
    
    def __init__(self, capture: LogCapture):
        super().__init__()
        self.capture = capture
        
    def emit(self, record: logging.LogRecord):
        """Emitir log record"""
        try:
            self.capture.add_log(record)
        except Exception:
            self.handleError(record)
