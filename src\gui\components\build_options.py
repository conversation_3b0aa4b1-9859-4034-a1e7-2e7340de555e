"""
Componente Opções de Build
Widget para configurar opções específicas de cada linguagem
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any

class BuildOptions(ttk.LabelFrame):
    """Widget para opções de build"""
    
    def __init__(self, parent, config_manager, project_type_var: tk.StringVar):
        super().__init__(parent, text="Opções de Build", padding="5")
        
        self.config_manager = config_manager
        self.project_type_var = project_type_var
        self.current_type = ""
        
        # Variáveis para opções comuns
        self.options_vars = {}
        
        self.create_widgets()
        self.setup_layout()
        self.bind_events()
        
    def create_widgets(self):
        """Criar widgets do componente"""
        
        # Frame principal para opções
        self.options_frame = ttk.Frame(self)
        
        # Label de status
        self.status_label = ttk.Label(
            self.options_frame,
            text="Selecione um projeto para ver as opções",
            foreground="gray"
        )
        
    def setup_layout(self):
        """Configurar layout"""
        
        self.columnconfigure(0, weight=1)
        self.options_frame.grid(row=0, column=0, sticky="ew")
        self.options_frame.columnconfigure(0, weight=1)
        
        self.status_label.grid(row=0, column=0, pady=10)
        
    def bind_events(self):
        """Vincular eventos"""
        
        self.project_type_var.trace_add("write", self._on_project_type_changed)
        
    def _on_project_type_changed(self, *args):
        """Callback quando tipo de projeto muda"""
        
        new_type = self.project_type_var.get()
        if new_type != self.current_type:
            self.update_for_project_type(new_type)
            
    def update_for_project_type(self, project_type: str):
        """Atualizar opções para tipo de projeto"""
        
        self.current_type = project_type
        
        # Limpar widgets existentes
        for widget in self.options_frame.winfo_children():
            widget.destroy()
            
        # Limpar variáveis
        self.options_vars.clear()
        
        if not project_type or project_type in ["Desconhecido", "Caminho inválido"]:
            self.status_label = ttk.Label(
                self.options_frame,
                text="Selecione um projeto válido para ver as opções",
                foreground="gray"
            )
            self.status_label.grid(row=0, column=0, pady=10)
            return
            
        # Criar opções específicas por tipo
        if project_type == "Python":
            self._create_python_options()
        elif project_type == "Node.js":
            self._create_nodejs_options()
        elif project_type.startswith("Java"):
            self._create_java_options()
        elif project_type == ".NET":
            self._create_dotnet_options()
        elif project_type == "Go":
            self._create_go_options()
        elif project_type == "Rust":
            self._create_rust_options()
        else:
            self._create_generic_options()
            
    def _create_python_options(self):
        """Criar opções para projetos Python"""
        
        config = self.config_manager.get_language_config('python')
        
        row = 0
        
        # Usar PyInstaller
        self.options_vars['use_pyinstaller'] = tk.BooleanVar(
            value=config.get('use_pyinstaller', True)
        )
        ttk.Checkbutton(
            self.options_frame,
            text="Usar PyInstaller",
            variable=self.options_vars['use_pyinstaller']
        ).grid(row=row, column=0, sticky="w", pady=2)
        row += 1
        
        # Arquivo único
        self.options_vars['one_file'] = tk.BooleanVar(
            value=config.get('one_file', True)
        )
        ttk.Checkbutton(
            self.options_frame,
            text="Gerar arquivo único",
            variable=self.options_vars['one_file']
        ).grid(row=row, column=0, sticky="w", pady=2)
        row += 1
        
        # Sem console
        self.options_vars['no_console'] = tk.BooleanVar(
            value=config.get('no_console', False)
        )
        ttk.Checkbutton(
            self.options_frame,
            text="Sem janela de console",
            variable=self.options_vars['no_console']
        ).grid(row=row, column=0, sticky="w", pady=2)
        row += 1
        
        # Arquivo principal
        ttk.Label(self.options_frame, text="Arquivo principal:").grid(
            row=row, column=0, sticky="w", pady=(10, 2)
        )
        row += 1
        
        self.options_vars['main_file'] = tk.StringVar(
            value=config.get('main_file', 'main.py')
        )
        ttk.Entry(
            self.options_frame,
            textvariable=self.options_vars['main_file'],
            width=30
        ).grid(row=row, column=0, sticky="w", pady=2)
        row += 1
        
    def _create_nodejs_options(self):
        """Criar opções para projetos Node.js"""
        
        config = self.config_manager.get_language_config('nodejs')
        
        row = 0
        
        # Usar pkg
        self.options_vars['use_pkg'] = tk.BooleanVar(
            value=config.get('use_pkg', True)
        )
        ttk.Checkbutton(
            self.options_frame,
            text="Usar pkg",
            variable=self.options_vars['use_pkg']
        ).grid(row=row, column=0, sticky="w", pady=2)
        row += 1
        
        # Comprimir
        self.options_vars['compress'] = tk.BooleanVar(
            value=config.get('compress', True)
        )
        ttk.Checkbutton(
            self.options_frame,
            text="Comprimir executável",
            variable=self.options_vars['compress']
        ).grid(row=row, column=0, sticky="w", pady=2)
        row += 1
        
        # Target
        ttk.Label(self.options_frame, text="Target:").grid(
            row=row, column=0, sticky="w", pady=(10, 2)
        )
        row += 1
        
        self.options_vars['target'] = tk.StringVar(
            value=config.get('targets', ['node18-win-x64'])[0]
        )
        target_combo = ttk.Combobox(
            self.options_frame,
            textvariable=self.options_vars['target'],
            values=['node18-win-x64', 'node18-linux-x64', 'node18-macos-x64'],
            state="readonly",
            width=25
        )
        target_combo.grid(row=row, column=0, sticky="w", pady=2)
        row += 1
        
    def _create_java_options(self):
        """Criar opções para projetos Java"""
        
        config = self.config_manager.get_language_config('java')
        
        row = 0
        
        # Classe principal
        ttk.Label(self.options_frame, text="Classe principal:").grid(
            row=row, column=0, sticky="w", pady=2
        )
        row += 1
        
        self.options_vars['main_class'] = tk.StringVar(
            value=config.get('main_class', '')
        )
        ttk.Entry(
            self.options_frame,
            textvariable=self.options_vars['main_class'],
            width=40
        ).grid(row=row, column=0, sticky="w", pady=2)
        row += 1
        
        # Nome do JAR
        ttk.Label(self.options_frame, text="Nome do JAR:").grid(
            row=row, column=0, sticky="w", pady=(10, 2)
        )
        row += 1
        
        self.options_vars['jar_name'] = tk.StringVar(
            value=config.get('jar_name', 'app.jar')
        )
        ttk.Entry(
            self.options_frame,
            textvariable=self.options_vars['jar_name'],
            width=30
        ).grid(row=row, column=0, sticky="w", pady=2)
        row += 1
        
    def _create_dotnet_options(self):
        """Criar opções para projetos .NET"""
        
        config = self.config_manager.get_language_config('dotnet')
        
        row = 0
        
        # Framework
        ttk.Label(self.options_frame, text="Framework:").grid(
            row=row, column=0, sticky="w", pady=2
        )
        row += 1
        
        self.options_vars['framework'] = tk.StringVar(
            value=config.get('framework', 'net6.0')
        )
        framework_combo = ttk.Combobox(
            self.options_frame,
            textvariable=self.options_vars['framework'],
            values=['net6.0', 'net7.0', 'net8.0', 'netcoreapp3.1'],
            state="readonly",
            width=20
        )
        framework_combo.grid(row=row, column=0, sticky="w", pady=2)
        row += 1
        
        # Runtime
        ttk.Label(self.options_frame, text="Runtime:").grid(
            row=row, column=0, sticky="w", pady=(10, 2)
        )
        row += 1
        
        self.options_vars['runtime'] = tk.StringVar(
            value=config.get('runtime', 'win-x64')
        )
        runtime_combo = ttk.Combobox(
            self.options_frame,
            textvariable=self.options_vars['runtime'],
            values=['win-x64', 'linux-x64', 'osx-x64'],
            state="readonly",
            width=20
        )
        runtime_combo.grid(row=row, column=0, sticky="w", pady=2)
        row += 1
        
        # Self-contained
        self.options_vars['self_contained'] = tk.BooleanVar(
            value=config.get('self_contained', True)
        )
        ttk.Checkbutton(
            self.options_frame,
            text="Self-contained",
            variable=self.options_vars['self_contained']
        ).grid(row=row, column=0, sticky="w", pady=(10, 2))
        row += 1
        
    def _create_go_options(self):
        """Criar opções para projetos Go"""
        
        config = self.config_manager.get_language_config('go')
        
        row = 0
        
        # Target OS
        ttk.Label(self.options_frame, text="Sistema operacional:").grid(
            row=row, column=0, sticky="w", pady=2
        )
        row += 1
        
        self.options_vars['target_os'] = tk.StringVar(
            value=config.get('target_os', 'windows')
        )
        os_combo = ttk.Combobox(
            self.options_frame,
            textvariable=self.options_vars['target_os'],
            values=['windows', 'linux', 'darwin'],
            state="readonly",
            width=20
        )
        os_combo.grid(row=row, column=0, sticky="w", pady=2)
        row += 1
        
        # Target Arch
        ttk.Label(self.options_frame, text="Arquitetura:").grid(
            row=row, column=0, sticky="w", pady=(10, 2)
        )
        row += 1
        
        self.options_vars['target_arch'] = tk.StringVar(
            value=config.get('target_arch', 'amd64')
        )
        arch_combo = ttk.Combobox(
            self.options_frame,
            textvariable=self.options_vars['target_arch'],
            values=['amd64', '386', 'arm64'],
            state="readonly",
            width=20
        )
        arch_combo.grid(row=row, column=0, sticky="w", pady=2)
        row += 1
        
    def _create_rust_options(self):
        """Criar opções para projetos Rust"""
        
        config = self.config_manager.get_language_config('rust')
        
        row = 0
        
        # Release mode
        self.options_vars['release_mode'] = tk.BooleanVar(
            value=config.get('release_mode', True)
        )
        ttk.Checkbutton(
            self.options_frame,
            text="Modo release (otimizado)",
            variable=self.options_vars['release_mode']
        ).grid(row=row, column=0, sticky="w", pady=2)
        row += 1
        
        # Target
        ttk.Label(self.options_frame, text="Target:").grid(
            row=row, column=0, sticky="w", pady=(10, 2)
        )
        row += 1
        
        self.options_vars['target'] = tk.StringVar(
            value=config.get('target', 'x86_64-pc-windows-msvc')
        )
        target_combo = ttk.Combobox(
            self.options_frame,
            textvariable=self.options_vars['target'],
            values=[
                'x86_64-pc-windows-msvc',
                'x86_64-unknown-linux-gnu',
                'x86_64-apple-darwin'
            ],
            state="readonly",
            width=30
        )
        target_combo.grid(row=row, column=0, sticky="w", pady=2)
        row += 1
        
    def _create_generic_options(self):
        """Criar opções genéricas"""
        
        ttk.Label(
            self.options_frame,
            text=f"Opções específicas para {self.current_type} não implementadas ainda.",
            foreground="orange"
        ).grid(row=0, column=0, pady=10)
        
    def get_options(self) -> Dict[str, Any]:
        """Obter opções atuais"""
        
        options = {}
        for key, var in self.options_vars.items():
            if isinstance(var, tk.BooleanVar):
                options[key] = var.get()
            elif isinstance(var, (tk.StringVar, tk.IntVar, tk.DoubleVar)):
                options[key] = var.get()
                
        return options
        
    def save_options(self):
        """Salvar opções nas configurações"""
        
        if self.current_type:
            language_key = self.current_type.lower().split()[0]  # Ex: "Java (Maven)" -> "java"
            options = self.get_options()
            self.config_manager.set_language_config(language_key, options)
