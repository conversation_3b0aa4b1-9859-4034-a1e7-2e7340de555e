"""
Gerador Node.js
Gerador de executáveis para projetos Node.js usando pkg
"""

import json
from pathlib import Path
from typing import List, Dict, Any
from .base_generator import BaseGenerator

class NodeJSGenerator(BaseGenerator):
    """Gerador de executáveis para Node.js"""
    
    def __init__(self, project_path: str, output_path: str, options: Dict, logger):
        super().__init__(project_path, output_path, options, logger)
        
        # Opções específicas do Node.js
        self.use_pkg = options.get('use_pkg', True)
        self.target = options.get('target', 'node18-win-x64')
        self.compress = options.get('compress', True)
        self.main_file = options.get('main_file', '')
        
    def check_dependencies(self) -> List[str]:
        """Verificar dependências necessárias"""
        
        missing = []
        
        # Verificar Node.js
        if not self.check_command_exists('node'):
            missing.append('Node.js')
            
        # Verificar npm
        if not self.check_command_exists('npm'):
            missing.append('npm')
            
        return missing
        
    def generate(self) -> bool:
        """Gerar executável Node.js"""
        
        try:
            self.update_progress(0, "Iniciando geração do executável Node.js...")
            
            # Verificar dependências
            missing_deps = self.check_dependencies()
            if missing_deps:
                self.logger.error(f"Dependências faltando: {', '.join(missing_deps)}")
                return False
                
            self.update_progress(10, "Dependências verificadas")
            
            # Verificar package.json
            package_json = self.project_path / 'package.json'
            if not package_json.exists():
                self.logger.error("package.json não encontrado")
                return False
                
            # Analisar package.json
            package_data = self._load_package_json()
            if not package_data:
                return False
                
            self.update_progress(20, "package.json analisado")
            
            # Determinar arquivo principal
            main_file = self._get_main_file(package_data)
            if not main_file:
                return False
                
            self.update_progress(30, f"Arquivo principal: {main_file}")
            
            # Instalar dependências
            self.update_progress(35, "Instalando dependências...")
            if not self._install_dependencies():
                return False
                
            self.update_progress(50, "Dependências instaladas")
            
            # Instalar pkg se necessário
            if self.use_pkg:
                self.update_progress(55, "Verificando pkg...")
                if not self._ensure_pkg_installed():
                    return False
                    
            self.update_progress(60, "pkg pronto")
            
            # Gerar executável
            if self.use_pkg:
                success = self._generate_with_pkg(main_file)
            else:
                self.logger.error("Apenas pkg é suportado atualmente")
                return False
                
            if success:
                self.update_progress(90, "Executável gerado, finalizando...")
                self._post_process()
                self.update_progress(100, "Executável Node.js gerado com sucesso!")
                return True
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"Erro ao gerar executável Node.js: {e}")
            return False
            
    def _load_package_json(self) -> Dict:
        """Carregar e validar package.json"""
        
        try:
            with open(self.project_path / 'package.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            self.logger.info(f"Projeto: {data.get('name', 'Sem nome')} v{data.get('version', '0.0.0')}")
            return data
            
        except Exception as e:
            self.logger.error(f"Erro ao ler package.json: {e}")
            return {}
            
    def _get_main_file(self, package_data: Dict) -> str:
        """Determinar arquivo principal"""
        
        # Usar arquivo especificado nas opções
        if self.main_file:
            main_path = self.project_path / self.main_file
            if main_path.exists():
                return self.main_file
            else:
                self.logger.warning(f"Arquivo principal especificado não existe: {self.main_file}")
                
        # Usar main do package.json
        main_from_package = package_data.get('main', 'index.js')
        main_path = self.project_path / main_from_package
        if main_path.exists():
            return main_from_package
            
        # Tentar arquivos comuns
        common_files = ['index.js', 'app.js', 'server.js', 'main.js']
        for file_name in common_files:
            file_path = self.project_path / file_name
            if file_path.exists():
                self.logger.info(f"Arquivo principal detectado: {file_name}")
                return file_name
                
        self.logger.error("Arquivo principal não encontrado")
        return None
        
    def _install_dependencies(self) -> bool:
        """Instalar dependências do projeto"""
        
        try:
            self.logger.info("Instalando dependências do projeto...")
            
            # Verificar se existe package-lock.json ou yarn.lock
            if (self.project_path / 'yarn.lock').exists():
                # Usar yarn se disponível
                if self.check_command_exists('yarn'):
                    result = self.run_command(['yarn', 'install'])
                else:
                    result = self.run_command(['npm', 'install'])
            else:
                # Usar npm
                result = self.run_command(['npm', 'install'])
                
            if result.returncode == 0:
                self.logger.info("Dependências instaladas com sucesso")
                return True
            else:
                self.logger.error(f"Falha ao instalar dependências: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"Erro ao instalar dependências: {e}")
            return False
            
    def _ensure_pkg_installed(self) -> bool:
        """Garantir que pkg está instalado"""
        
        try:
            # Verificar se pkg está instalado globalmente
            result = self.run_command(['pkg', '--version'])
            if result.returncode == 0:
                self.logger.info(f"pkg já instalado: {result.stdout.strip()}")
                return True
                
        except:
            pass
            
        try:
            # Instalar pkg globalmente
            self.logger.info("Instalando pkg...")
            result = self.run_command(['npm', 'install', '-g', 'pkg'])
            
            if result.returncode == 0:
                self.logger.info("pkg instalado com sucesso")
                return True
            else:
                self.logger.error(f"Falha ao instalar pkg: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"Erro ao instalar pkg: {e}")
            return False
            
    def _generate_with_pkg(self, main_file: str) -> bool:
        """Gerar executável usando pkg"""
        
        try:
            # Construir comando pkg
            cmd = ['pkg']
            
            # Arquivo principal
            cmd.append(main_file)
            
            # Target
            cmd.extend(['--target', self.target])
            
            # Diretório de saída
            output_name = self.get_project_name()
            if self.target.endswith('win-x64') and not output_name.endswith('.exe'):
                output_name += '.exe'
                
            output_file = self.output_path / output_name
            cmd.extend(['--output', str(output_file)])
            
            # Compressão
            if self.compress:
                cmd.append('--compress')
                
            self.update_progress(70, "Executando pkg...")
            self.logger.info(f"Comando pkg: {' '.join(cmd)}")
            
            # Executar pkg
            result = self.run_command(cmd, cwd=self.project_path)
            
            if result.returncode == 0:
                self.logger.info("pkg executado com sucesso")
                return True
            else:
                self.logger.error(f"pkg falhou: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"Erro ao executar pkg: {e}")
            return False
            
    def _post_process(self):
        """Pós-processamento após geração"""
        
        try:
            # Copiar arquivos adicionais se especificado
            additional_files = self.options.get('additional_files', [])
            if additional_files:
                self.copy_additional_files(additional_files)
                
            # Copiar assets comuns
            common_assets = ['assets', 'public', 'static', 'resources']
            for asset_dir in common_assets:
                asset_path = self.project_path / asset_dir
                if asset_path.exists() and asset_path.is_dir():
                    self.copy_additional_files([asset_dir])
                    
        except Exception as e:
            self.logger.warning(f"Erro no pós-processamento: {e}")
            
    def get_executable_info(self) -> Dict[str, Any]:
        """Obter informações do executável gerado"""
        
        info = {
            'type': 'Node.js',
            'generator': 'pkg' if self.use_pkg else 'Unknown',
            'target': self.target,
            'compress': self.compress,
            'main_file': self.main_file
        }
        
        # Verificar se executável foi gerado
        exe_path = self.get_output_executable_path()
        if exe_path.exists():
            info['executable_path'] = str(exe_path)
            info['size_mb'] = round(exe_path.stat().st_size / (1024 * 1024), 2)
            
        return info
