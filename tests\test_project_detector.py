"""
Testes para o Detector de Projetos
"""

import unittest
import tempfile
import shutil
from pathlib import Path
import json

# Adicionar src ao path
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from core.project_detector import ProjectDetector

class TestProjectDetector(unittest.TestCase):
    """Testes para ProjectDetector"""
    
    def setUp(self):
        """Configurar testes"""
        self.detector = ProjectDetector()
        self.temp_dir = Path(tempfile.mkdtemp())
        
    def tearDown(self):
        """Limpar após testes"""
        shutil.rmtree(self.temp_dir)
        
    def test_detect_python_project(self):
        """Testar detecção de projeto Python"""
        
        # Criar arquivos Python
        (self.temp_dir / 'main.py').write_text('print("Hello World")')
        (self.temp_dir / 'requirements.txt').write_text('requests>=2.0.0')
        
        result = self.detector.detect_project(str(self.temp_dir))
        
        self.assertEqual(result['type'], 'python')
        self.assertGreater(result['confidence'], 50)
        self.assertIn('python_files', result['details'])
        self.assertTrue(result['details']['has_requirements'])
        
    def test_detect_nodejs_project(self):
        """Testar detecção de projeto Node.js"""
        
        # Criar package.json
        package_data = {
            'name': 'test-app',
            'version': '1.0.0',
            'main': 'index.js',
            'dependencies': {
                'express': '^4.18.0'
            }
        }
        
        (self.temp_dir / 'package.json').write_text(json.dumps(package_data))
        (self.temp_dir / 'index.js').write_text('console.log("Hello World");')
        
        result = self.detector.detect_project(str(self.temp_dir))
        
        self.assertEqual(result['type'], 'nodejs')
        self.assertGreater(result['confidence'], 50)
        self.assertEqual(result['details']['package_name'], 'test-app')
        self.assertEqual(result['details']['main'], 'index.js')
        
    def test_detect_java_maven_project(self):
        """Testar detecção de projeto Java Maven"""
        
        # Criar pom.xml básico
        pom_content = '''<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.example</groupId>
    <artifactId>test-app</artifactId>
    <version>1.0.0</version>
</project>'''
        
        (self.temp_dir / 'pom.xml').write_text(pom_content)
        (self.temp_dir / 'Main.java').write_text('public class Main {}')
        
        result = self.detector.detect_project(str(self.temp_dir))
        
        self.assertEqual(result['type'], 'java')
        self.assertGreater(result['confidence'], 50)
        self.assertEqual(result['details']['build_tool'], 'maven')
        
    def test_detect_unknown_project(self):
        """Testar detecção de projeto desconhecido"""
        
        # Criar apenas arquivo de texto
        (self.temp_dir / 'readme.txt').write_text('This is a text file')
        
        result = self.detector.detect_project(str(self.temp_dir))
        
        self.assertEqual(result['type'], 'unknown')
        self.assertEqual(result['confidence'], 0)
        
    def test_detect_invalid_path(self):
        """Testar detecção com caminho inválido"""
        
        result = self.detector.detect_project('/path/that/does/not/exist')
        
        self.assertEqual(result['type'], 'unknown')
        self.assertEqual(result['confidence'], 0)
        self.assertIn('error', result)

if __name__ == '__main__':
    unittest.main()
