#!/usr/bin/env python3
"""
Teste simples do executável
"""

import subprocess
import sys
from pathlib import Path

def test_executable():
    """Testar executável"""
    
    exe_path = Path("output_test_debug/main.exe")
    
    if not exe_path.exists():
        print("❌ Executável não encontrado!")
        return False
        
    print(f"🧪 Testando executável: {exe_path}")
    print(f"   Tamanho: {exe_path.stat().st_size / (1024*1024):.1f} MB")
    
    try:
        # Executar com timeout
        result = subprocess.run(
            [str(exe_path)],
            timeout=5,
            capture_output=True,
            text=True
        )
        
        print(f"   Código de saída: {result.returncode}")
        print(f"   Saída: {result.stdout}")
        if result.stderr:
            print(f"   Erro: {result.stderr}")
            
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("   ⏰ Executável rodou por mais de 5 segundos (provavelmente GUI aberta)")
        return True
    except Exception as e:
        print(f"   ❌ Erro ao executar: {e}")
        return False

if __name__ == '__main__':
    success = test_executable()
    print(f"\n{'✅ Sucesso' if success else '❌ Falha'}")
    sys.exit(0 if success else 1)
