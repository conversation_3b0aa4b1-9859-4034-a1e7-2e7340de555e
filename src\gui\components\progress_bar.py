"""
Componente Barra de Progresso
Widget para mostrar progresso de operações
"""

import tkinter as tk
from tkinter import ttk

class ProgressBar(ttk.Frame):
    """Widget de barra de progresso com label"""
    
    def __init__(self, parent):
        super().__init__(parent)
        
        self.create_widgets()
        self.setup_layout()
        
    def create_widgets(self):
        """Criar widgets do componente"""
        
        # Label de status
        self.status_label = ttk.Label(self, text="Pronto")
        
        # Barra de progresso
        self.progress_bar = ttk.Progressbar(
            self,
            mode='determinate',
            length=400
        )
        
        # Label de porcentagem
        self.percent_label = ttk.Label(self, text="0%")
        
    def setup_layout(self):
        """Configurar layout"""
        
        self.columnconfigure(1, weight=1)
        
        self.status_label.grid(row=0, column=0, sticky="w", padx=(0, 10))
        self.progress_bar.grid(row=0, column=1, sticky="ew", padx=(0, 10))
        self.percent_label.grid(row=0, column=2, sticky="e")
        
    def start(self, message: str = "Processando..."):
        """Iniciar progresso indeterminado"""
        
        self.status_label.config(text=message)
        self.progress_bar.config(mode='indeterminate')
        self.progress_bar.start(10)
        self.percent_label.config(text="")
        
    def stop(self):
        """Parar progresso"""
        
        self.progress_bar.stop()
        self.progress_bar.config(mode='determinate', value=0)
        self.status_label.config(text="Pronto")
        self.percent_label.config(text="0%")
        
    def set_progress(self, value: int, message: str = None):
        """
        Definir progresso específico
        
        Args:
            value: Valor de 0 a 100
            message: Mensagem opcional
        """
        
        self.progress_bar.config(mode='determinate', value=value)
        self.percent_label.config(text=f"{value}%")
        
        if message:
            self.status_label.config(text=message)
            
    def set_message(self, message: str):
        """Definir apenas a mensagem"""
        
        self.status_label.config(text=message)
