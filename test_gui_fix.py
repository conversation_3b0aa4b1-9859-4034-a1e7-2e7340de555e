#!/usr/bin/env python3
"""
Script de teste para verificar a correção do problema de interface GUI
"""

import sys
import os
from pathlib import Path

# Adicionar src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from core.project_detector import ProjectDetector
from generators.generator_factory import GeneratorFactory
from utils.logger import setup_logger

def test_console_version():
    """Testar build com console habilitado"""
    
    logger = setup_logger()
    
    # Caminhos
    project_path = Path(__file__).parent / 'exemplo_python'
    output_path = Path(__file__).parent / 'output_test_fixed'
    
    print(f"🧪 Testando build com CONSOLE habilitado...")
    print(f"   Projeto: {project_path}")
    print(f"   Saída: {output_path}")
    
    # Verificar se projeto existe
    if not project_path.exists():
        print("❌ Projeto de exemplo não encontrado!")
        return False
        
    # Detectar projeto
    detector = ProjectDetector()
    project_info = detector.detect_project(str(project_path))
    
    print(f"   Tipo detectado: {project_info['type']}")
    print(f"   Confiança: {project_info['confidence']}%")
    
    if project_info['type'] != 'python':
        print("❌ Projeto não foi detectado como Python!")
        return False
        
    # Configurar opções de build - COM CONSOLE
    options = {
        'use_pyinstaller': True,
        'one_file': True,
        'no_console': False,  # Console habilitado para debug
        'main_file': 'main.py'
    }
    
    # Criar gerador
    generator = GeneratorFactory.create_generator(
        'python', str(project_path), str(output_path), options, logger
    )
    
    if not generator:
        print("❌ Não foi possível criar gerador!")
        return False
        
    # Verificar dependências
    missing_deps = generator.check_dependencies()
    if missing_deps:
        print(f"❌ Dependências faltando: {', '.join(missing_deps)}")
        return False
        
    print("✅ Dependências verificadas")
    
    # Callback de progresso
    def progress_callback(percentage, message):
        print(f"   Progresso: {percentage}% - {message}")
        
    generator.set_progress_callback(progress_callback)
    
    # Gerar executável
    print("🔨 Iniciando geração...")
    success = generator.generate()
    
    if success:
        print("✅ Executável com console gerado com sucesso!")
        
        # Verificar se arquivo foi criado
        exe_info = generator.get_executable_info()
        if 'executable_path' in exe_info:
            exe_path = Path(exe_info['executable_path'])
            if exe_path.exists():
                print(f"   Arquivo: {exe_path}")
                print(f"   Tamanho: {exe_info.get('size_mb', 'N/A')} MB")
                return True
            else:
                print("❌ Arquivo executável não encontrado!")
                return False
        else:
            print("⚠️  Executável gerado mas caminho não informado")
            return True
    else:
        print("❌ Falha na geração do executável!")
        return False

def test_windowed_version():
    """Testar build sem console (windowed)"""
    
    logger = setup_logger()
    
    # Caminhos
    project_path = Path(__file__).parent / 'exemplo_python'
    output_path = Path(__file__).parent / 'output_test_windowed'
    
    print(f"\n🧪 Testando build SEM CONSOLE (windowed)...")
    print(f"   Projeto: {project_path}")
    print(f"   Saída: {output_path}")
    
    # Configurar opções de build - SEM CONSOLE
    options = {
        'use_pyinstaller': True,
        'one_file': True,
        'no_console': True,  # Sem console - versão final
        'main_file': 'main.py'
    }
    
    # Criar gerador
    generator = GeneratorFactory.create_generator(
        'python', str(project_path), str(output_path), options, logger
    )
    
    if not generator:
        print("❌ Não foi possível criar gerador!")
        return False
        
    # Callback de progresso
    def progress_callback(percentage, message):
        print(f"   Progresso: {percentage}% - {message}")
        
    generator.set_progress_callback(progress_callback)
    
    # Gerar executável
    print("🔨 Iniciando geração...")
    success = generator.generate()
    
    if success:
        print("✅ Executável windowed gerado com sucesso!")
        print("   📝 Versão debug também foi criada automaticamente")
        print("   📄 Arquivo LEIA-ME.txt criado com instruções")
        
        # Verificar se arquivo foi criado
        exe_info = generator.get_executable_info()
        if 'executable_path' in exe_info:
            exe_path = Path(exe_info['executable_path'])
            if exe_path.exists():
                print(f"   Arquivo principal: {exe_path}")
                print(f"   Tamanho: {exe_info.get('size_mb', 'N/A')} MB")
                
                # Verificar se versão debug foi criada
                debug_path = exe_path.parent / f"{exe_path.stem}_debug.exe"
                if debug_path.exists():
                    print(f"   Arquivo debug: {debug_path}")
                
                # Verificar se arquivo de instruções foi criado
                readme_path = exe_path.parent / "LEIA-ME.txt"
                if readme_path.exists():
                    print(f"   Instruções: {readme_path}")
                
                return True
            else:
                print("❌ Arquivo executável não encontrado!")
                return False
        else:
            print("⚠️  Executável gerado mas caminho não informado")
            return True
    else:
        print("❌ Falha na geração do executável!")
        return False

def main():
    """Função principal"""
    
    print("🚀 Teste de Correção - Interface GUI")
    print("=" * 60)
    
    try:
        # Testar versão com console
        console_success = test_console_version()
        
        # Testar versão windowed
        windowed_success = test_windowed_version()
        
        print("\n" + "=" * 60)
        print("📊 RESULTADOS:")
        print(f"   Console habilitado: {'✅ Sucesso' if console_success else '❌ Falhou'}")
        print(f"   Windowed (sem console): {'✅ Sucesso' if windowed_success else '❌ Falhou'}")
        
        if console_success and windowed_success:
            print("\n🎉 Todos os testes passaram!")
            print("\n💡 RECOMENDAÇÕES:")
            print("   1. Use a versão COM CONSOLE durante desenvolvimento")
            print("   2. Use a versão SEM CONSOLE para distribuição final")
            print("   3. Se a versão sem console não funcionar, use a versão debug")
            return 0
        elif console_success:
            print("\n⚠️  Versão com console funcionou, mas windowed falhou")
            print("   Use a versão com console por enquanto")
            return 1
        else:
            print("\n❌ Ambos os testes falharam!")
            return 1
            
    except Exception as e:
        print(f"\n❌ Erro durante teste: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
