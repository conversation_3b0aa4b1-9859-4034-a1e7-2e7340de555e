"""
Utilitários de Arquivo
Funções auxiliares para manipulação de arquivos e diretórios
"""

import os
import shutil
import hashlib
from pathlib import Path
from typing import List, Optional, Dict, Any
import json

def ensure_directory(path: Path) -> bool:
    """
    Garantir que diretório existe
    
    Args:
        path: Caminho do diretório
        
    Returns:
        True se diretório existe ou foi criado
    """
    
    try:
        path.mkdir(parents=True, exist_ok=True)
        return True
    except Exception:
        return False

def copy_file_safe(src: Path, dst: Path) -> bool:
    """
    Copiar arquivo com segurança
    
    Args:
        src: Arquivo origem
        dst: Arquivo destino
        
    Returns:
        True se copiado com sucesso
    """
    
    try:
        # Garantir que diretório de destino existe
        ensure_directory(dst.parent)
        
        # Copiar arquivo
        shutil.copy2(src, dst)
        return True
        
    except Exception:
        return False

def get_file_hash(file_path: Path, algorithm: str = 'md5') -> Optional[str]:
    """
    Calcular hash de arquivo
    
    Args:
        file_path: Caminho do arquivo
        algorithm: Algoritmo de hash (md5, sha1, sha256)
        
    Returns:
        Hash do arquivo ou None se erro
    """
    
    try:
        hash_obj = hashlib.new(algorithm)
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_obj.update(chunk)
                
        return hash_obj.hexdigest()
        
    except Exception:
        return None

def get_directory_size(path: Path) -> int:
    """
    Calcular tamanho total de diretório
    
    Args:
        path: Caminho do diretório
        
    Returns:
        Tamanho em bytes
    """
    
    total_size = 0
    
    try:
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                file_path = Path(dirpath) / filename
                if file_path.exists():
                    total_size += file_path.stat().st_size
                    
    except Exception:
        pass
        
    return total_size

def format_file_size(size_bytes: int) -> str:
    """
    Formatar tamanho de arquivo para leitura humana
    
    Args:
        size_bytes: Tamanho em bytes
        
    Returns:
        Tamanho formatado (ex: "1.5 MB")
    """
    
    if size_bytes == 0:
        return "0 B"
        
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
        
    return f"{size_bytes:.1f} {size_names[i]}"

def find_files_by_extension(directory: Path, extensions: List[str]) -> List[Path]:
    """
    Encontrar arquivos por extensão
    
    Args:
        directory: Diretório para buscar
        extensions: Lista de extensões (ex: ['.py', '.js'])
        
    Returns:
        Lista de arquivos encontrados
    """
    
    files = []
    
    try:
        for ext in extensions:
            pattern = f"**/*{ext}"
            files.extend(directory.glob(pattern))
            
    except Exception:
        pass
        
    return files

def clean_directory(directory: Path, keep_files: List[str] = None) -> bool:
    """
    Limpar diretório mantendo arquivos específicos
    
    Args:
        directory: Diretório para limpar
        keep_files: Lista de arquivos para manter
        
    Returns:
        True se limpeza foi bem-sucedida
    """
    
    if not directory.exists():
        return True
        
    keep_files = keep_files or []
    
    try:
        for item in directory.iterdir():
            if item.name not in keep_files:
                if item.is_file():
                    item.unlink()
                elif item.is_dir():
                    shutil.rmtree(item)
                    
        return True
        
    except Exception:
        return False

def backup_file(file_path: Path, backup_suffix: str = '.bak') -> Optional[Path]:
    """
    Criar backup de arquivo
    
    Args:
        file_path: Arquivo para fazer backup
        backup_suffix: Sufixo do backup
        
    Returns:
        Caminho do backup ou None se erro
    """
    
    if not file_path.exists():
        return None
        
    backup_path = file_path.with_suffix(file_path.suffix + backup_suffix)
    
    try:
        shutil.copy2(file_path, backup_path)
        return backup_path
        
    except Exception:
        return None

def load_json_file(file_path: Path) -> Optional[Dict[str, Any]]:
    """
    Carregar arquivo JSON
    
    Args:
        file_path: Caminho do arquivo JSON
        
    Returns:
        Dados do JSON ou None se erro
    """
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
            
    except Exception:
        return None

def save_json_file(file_path: Path, data: Dict[str, Any], indent: int = 2) -> bool:
    """
    Salvar arquivo JSON
    
    Args:
        file_path: Caminho do arquivo
        data: Dados para salvar
        indent: Indentação
        
    Returns:
        True se salvo com sucesso
    """
    
    try:
        ensure_directory(file_path.parent)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=indent, ensure_ascii=False)
            
        return True
        
    except Exception:
        return False

def is_binary_file(file_path: Path) -> bool:
    """
    Verificar se arquivo é binário
    
    Args:
        file_path: Caminho do arquivo
        
    Returns:
        True se arquivo é binário
    """
    
    try:
        with open(file_path, 'rb') as f:
            chunk = f.read(1024)
            
        # Verificar se contém bytes nulos (indicativo de arquivo binário)
        return b'\x00' in chunk
        
    except Exception:
        return True  # Assumir binário se não conseguir ler

def get_relative_path(file_path: Path, base_path: Path) -> Path:
    """
    Obter caminho relativo
    
    Args:
        file_path: Caminho do arquivo
        base_path: Caminho base
        
    Returns:
        Caminho relativo
    """
    
    try:
        return file_path.relative_to(base_path)
    except ValueError:
        return file_path
