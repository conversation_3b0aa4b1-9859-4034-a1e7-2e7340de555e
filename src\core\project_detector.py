"""
Detector de Projetos
Módulo para detectar automaticamente o tipo e configurações de projetos
"""

import os
import json
import xml.etree.ElementTree as ET
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import re

class ProjectDetector:
    """Detector de tipos de projeto e suas configurações"""
    
    def __init__(self):
        self.detectors = {
            'python': self._detect_python,
            'nodejs': self._detect_nodejs,
            'java': self._detect_java,
            'dotnet': self._detect_dotnet,
            'go': self._detect_go,
            'rust': self._detect_rust,
            'cpp': self._detect_cpp
        }
        
    def detect_project(self, project_path: str) -> Dict:
        """
        Detectar tipo e configurações do projeto
        
        Args:
            project_path: Caminho do projeto
            
        Returns:
            Dicionário com informações do projeto
        """
        
        path = Path(project_path)
        if not path.exists() or not path.is_dir():
            return {
                'type': 'unknown',
                'confidence': 0,
                'details': {},
                'error': 'Caminho inválido'
            }
            
        results = []
        
        # Executar todos os detectores
        for lang, detector in self.detectors.items():
            try:
                result = detector(path)
                if result['confidence'] > 0:
                    result['type'] = lang
                    results.append(result)
            except Exception as e:
                print(f"Erro ao detectar {lang}: {e}")
                
        # Retornar resultado com maior confiança
        if results:
            best_result = max(results, key=lambda x: x['confidence'])
            return best_result
        else:
            return {
                'type': 'unknown',
                'confidence': 0,
                'details': {},
                'error': 'Tipo de projeto não reconhecido'
            }
            
    def _detect_python(self, path: Path) -> Dict:
        """Detectar projeto Python"""
        
        confidence = 0
        details = {}
        
        files = [f.name for f in path.iterdir() if f.is_file()]
        
        # Verificar arquivos característicos
        python_files = [f for f in files if f.endswith('.py')]
        
        if python_files:
            confidence += 30
            details['python_files'] = python_files
            
        # Arquivos de configuração
        if 'requirements.txt' in files:
            confidence += 25
            details['has_requirements'] = True
            
        if 'setup.py' in files:
            confidence += 20
            details['has_setup'] = True
            
        if 'pyproject.toml' in files:
            confidence += 20
            details['has_pyproject'] = True
            
        if 'Pipfile' in files:
            confidence += 15
            details['has_pipfile'] = True
            
        # Arquivos principais comuns
        main_files = ['main.py', 'app.py', '__main__.py', 'run.py']
        found_main = [f for f in main_files if f in files]
        if found_main:
            confidence += 15
            details['main_file'] = found_main[0]
            
        # Verificar estrutura de pacote
        if '__init__.py' in files:
            confidence += 10
            details['is_package'] = True
            
        # Detectar framework
        details['framework'] = self._detect_python_framework(path)
        if details['framework']:
            confidence += 10
            
        return {
            'confidence': min(confidence, 100),
            'details': details
        }
        
    def _detect_nodejs(self, path: Path) -> Dict:
        """Detectar projeto Node.js"""
        
        confidence = 0
        details = {}
        
        files = [f.name for f in path.iterdir() if f.is_file()]
        
        # package.json é obrigatório
        if 'package.json' not in files:
            return {'confidence': 0, 'details': {}}
            
        confidence += 50
        
        # Analisar package.json
        try:
            with open(path / 'package.json', 'r', encoding='utf-8') as f:
                package_data = json.load(f)
                
            details['package_name'] = package_data.get('name', '')
            details['version'] = package_data.get('version', '')
            details['main'] = package_data.get('main', 'index.js')
            details['scripts'] = package_data.get('scripts', {})
            
            # Verificar dependências
            deps = package_data.get('dependencies', {})
            dev_deps = package_data.get('devDependencies', {})
            
            details['dependencies'] = list(deps.keys())
            details['dev_dependencies'] = list(dev_deps.keys())
            
            # Detectar framework
            details['framework'] = self._detect_nodejs_framework(deps, dev_deps)
            
        except Exception as e:
            details['package_error'] = str(e)
            
        # Verificar lock files
        if 'package-lock.json' in files:
            confidence += 10
            details['has_npm_lock'] = True
            
        if 'yarn.lock' in files:
            confidence += 10
            details['has_yarn_lock'] = True
            
        # Verificar arquivo principal
        main_file = details.get('main', 'index.js')
        if main_file in files:
            confidence += 15
            
        return {
            'confidence': min(confidence, 100),
            'details': details
        }
        
    def _detect_java(self, path: Path) -> Dict:
        """Detectar projeto Java"""
        
        confidence = 0
        details = {}
        
        files = [f.name for f in path.iterdir() if f.is_file()]
        
        # Verificar arquivos Java
        java_files = [f for f in files if f.endswith('.java')]
        if java_files:
            confidence += 30
            details['java_files'] = java_files
            
        # Maven
        if 'pom.xml' in files:
            confidence += 40
            details['build_tool'] = 'maven'
            
            # Analisar pom.xml
            try:
                tree = ET.parse(path / 'pom.xml')
                root = tree.getroot()
                
                # Namespace Maven
                ns = {'maven': 'http://maven.apache.org/POM/4.0.0'}
                
                artifact_id = root.find('.//maven:artifactId', ns)
                if artifact_id is not None:
                    details['artifact_id'] = artifact_id.text
                    
                version = root.find('.//maven:version', ns)
                if version is not None:
                    details['version'] = version.text
                    
            except Exception as e:
                details['pom_error'] = str(e)
                
        # Gradle
        gradle_files = ['build.gradle', 'build.gradle.kts']
        found_gradle = [f for f in gradle_files if f in files]
        if found_gradle:
            confidence += 40
            details['build_tool'] = 'gradle'
            details['gradle_file'] = found_gradle[0]
            
        # Estrutura de diretórios Maven/Gradle
        src_main_java = path / 'src' / 'main' / 'java'
        if src_main_java.exists():
            confidence += 20
            details['has_standard_structure'] = True
            
        return {
            'confidence': min(confidence, 100),
            'details': details
        }
        
    def _detect_dotnet(self, path: Path) -> Dict:
        """Detectar projeto .NET"""
        
        confidence = 0
        details = {}
        
        files = [f.name for f in path.iterdir() if f.is_file()]
        
        # Arquivos de projeto
        csproj_files = [f for f in files if f.endswith('.csproj')]
        if csproj_files:
            confidence += 50
            details['project_file'] = csproj_files[0]
            
            # Analisar .csproj
            try:
                tree = ET.parse(path / csproj_files[0])
                root = tree.getroot()
                
                # Target Framework
                target_framework = root.find('.//TargetFramework')
                if target_framework is not None:
                    details['target_framework'] = target_framework.text
                    
                # Output Type
                output_type = root.find('.//OutputType')
                if output_type is not None:
                    details['output_type'] = output_type.text
                    
            except Exception as e:
                details['csproj_error'] = str(e)
                
        # Arquivos de solução
        sln_files = [f for f in files if f.endswith('.sln')]
        if sln_files:
            confidence += 20
            details['solution_file'] = sln_files[0]
            
        # Arquivos C#
        cs_files = [f for f in files if f.endswith('.cs')]
        if cs_files:
            confidence += 30
            details['cs_files'] = cs_files
            
        return {
            'confidence': min(confidence, 100),
            'details': details
        }
        
    def _detect_go(self, path: Path) -> Dict:
        """Detectar projeto Go"""
        
        confidence = 0
        details = {}
        
        files = [f.name for f in path.iterdir() if f.is_file()]
        
        # go.mod é obrigatório para projetos modernos
        if 'go.mod' in files:
            confidence += 50
            
            try:
                with open(path / 'go.mod', 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # Extrair nome do módulo
                module_match = re.search(r'module\s+(.+)', content)
                if module_match:
                    details['module_name'] = module_match.group(1).strip()
                    
                # Extrair versão do Go
                go_match = re.search(r'go\s+([\d.]+)', content)
                if go_match:
                    details['go_version'] = go_match.group(1)
                    
            except Exception as e:
                details['go_mod_error'] = str(e)
                
        # Arquivos Go
        go_files = [f for f in files if f.endswith('.go')]
        if go_files:
            confidence += 40
            details['go_files'] = go_files
            
            # Procurar main.go
            if 'main.go' in go_files:
                confidence += 10
                details['has_main'] = True
                
        return {
            'confidence': min(confidence, 100),
            'details': details
        }
        
    def _detect_rust(self, path: Path) -> Dict:
        """Detectar projeto Rust"""
        
        confidence = 0
        details = {}
        
        files = [f.name for f in path.iterdir() if f.is_file()]
        
        # Cargo.toml é obrigatório
        if 'Cargo.toml' not in files:
            return {'confidence': 0, 'details': {}}
            
        confidence += 60
        
        # Analisar Cargo.toml
        try:
            import toml
            with open(path / 'Cargo.toml', 'r', encoding='utf-8') as f:
                cargo_data = toml.load(f)
                
            package = cargo_data.get('package', {})
            details['name'] = package.get('name', '')
            details['version'] = package.get('version', '')
            details['edition'] = package.get('edition', '')
            
            # Dependências
            deps = cargo_data.get('dependencies', {})
            details['dependencies'] = list(deps.keys())
            
        except Exception as e:
            details['cargo_error'] = str(e)
            
        # Arquivos Rust
        rs_files = [f for f in files if f.endswith('.rs')]
        if rs_files:
            confidence += 20
            details['rs_files'] = rs_files
            
        # Estrutura src/
        src_dir = path / 'src'
        if src_dir.exists():
            confidence += 10
            
            if (src_dir / 'main.rs').exists():
                confidence += 10
                details['has_main'] = True
                details['project_type'] = 'binary'
            elif (src_dir / 'lib.rs').exists():
                confidence += 10
                details['has_lib'] = True
                details['project_type'] = 'library'
                
        return {
            'confidence': min(confidence, 100),
            'details': details
        }
        
    def _detect_cpp(self, path: Path) -> Dict:
        """Detectar projeto C/C++"""
        
        confidence = 0
        details = {}
        
        files = [f.name for f in path.iterdir() if f.is_file()]
        
        # Arquivos C/C++
        cpp_extensions = ['.c', '.cpp', '.cxx', '.cc', '.C']
        header_extensions = ['.h', '.hpp', '.hxx', '.hh']
        
        cpp_files = [f for f in files if any(f.endswith(ext) for ext in cpp_extensions)]
        header_files = [f for f in files if any(f.endswith(ext) for ext in header_extensions)]
        
        if cpp_files:
            confidence += 40
            details['source_files'] = cpp_files
            
        if header_files:
            confidence += 20
            details['header_files'] = header_files
            
        # Sistemas de build
        if 'CMakeLists.txt' in files:
            confidence += 30
            details['build_system'] = 'cmake'
            
        if 'Makefile' in files:
            confidence += 25
            details['build_system'] = 'make'
            
        if 'meson.build' in files:
            confidence += 25
            details['build_system'] = 'meson'
            
        return {
            'confidence': min(confidence, 100),
            'details': details
        }
        
    def _detect_python_framework(self, path: Path) -> Optional[str]:
        """Detectar framework Python"""
        
        try:
            # Verificar requirements.txt
            req_file = path / 'requirements.txt'
            if req_file.exists():
                with open(req_file, 'r', encoding='utf-8') as f:
                    content = f.read().lower()
                    
                if 'django' in content:
                    return 'Django'
                elif 'flask' in content:
                    return 'Flask'
                elif 'fastapi' in content:
                    return 'FastAPI'
                elif 'streamlit' in content:
                    return 'Streamlit'
                elif 'tkinter' in content or 'tk' in content:
                    return 'Tkinter'
                    
        except Exception:
            pass
            
        return None
        
    def _detect_nodejs_framework(self, deps: Dict, dev_deps: Dict) -> Optional[str]:
        """Detectar framework Node.js"""
        
        all_deps = {**deps, **dev_deps}
        
        if 'react' in all_deps:
            return 'React'
        elif 'vue' in all_deps:
            return 'Vue.js'
        elif 'angular' in all_deps or '@angular/core' in all_deps:
            return 'Angular'
        elif 'express' in all_deps:
            return 'Express'
        elif 'next' in all_deps:
            return 'Next.js'
        elif 'nuxt' in all_deps:
            return 'Nuxt.js'
        elif 'electron' in all_deps:
            return 'Electron'
            
        return None
