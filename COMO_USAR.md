# 🚀 Como Usar o Gerador de Executáveis

Este guia mostra como usar o aplicativo para gerar executáveis de seus projetos.

## 📋 Pré-requisitos

1. **Python 3.8+** instalado
2. **Dependências instaladas** (execute `python install.py`)
3. **Ferramentas específicas** para cada linguagem (opcional):
   - Node.js + npm (para projetos Node.js)
   - Java + Maven/Gradle (para projetos Java)
   - .NET SDK (para projetos .NET)
   - Go (para projetos Go)
   - Rust + Cargo (para projetos Rust)

## 🎯 Passo a Passo

### 1. Iniciar o Aplicativo

```bash
python main.py
```

### 2. Selecionar Projeto

1. Clique em **"Procurar"** na seção "Projeto"
2. Navegue até a pasta do seu projeto
3. Selecione a pasta do projeto
4. O tipo será detectado automaticamente

### 3. Configurar Opções

As opções variam conforme o tipo de projeto:

#### **Python**
- ✅ **Usar PyInstaller**: Ferramenta de empacotamento
- ✅ **Gerar arquivo único**: Um único .exe vs pasta com arquivos
- ⬜ **Sem janela de console**: Para aplicações GUI
- 📄 **Arquivo principal**: main.py, app.py, etc.

#### **Node.js**
- ✅ **Usar pkg**: Ferramenta de empacotamento
- 🎯 **Target**: node18-win-x64, node18-linux-x64, etc.
- ✅ **Comprimir executável**: Reduzir tamanho

### 4. Definir Pasta de Saída

1. Escolha onde salvar o executável
2. Padrão: `~/Desktop/Executaveis`
3. Clique em **"Procurar"** para alterar

### 5. Gerar Executável

1. Clique em **"Gerar Executável"**
2. Acompanhe o progresso na barra
3. Veja os logs na aba "Logs"
4. Aguarde a conclusão

## 📁 Exemplos Incluídos

O projeto inclui exemplos prontos para teste:

### Exemplo Python
```
exemplo_python/
├── main.py           # Aplicação GUI simples
└── requirements.txt  # Dependências (vazio)
```

### Exemplo Node.js
```
exemplo_nodejs/
├── package.json      # Configuração do projeto
└── index.js          # Aplicação console interativa
```

## 🔧 Resolução de Problemas

### Erro: "Dependências faltando: PyInstaller"
**Solução**: Execute `python install.py` novamente

### Erro: "Arquivo principal não encontrado"
**Solução**: 
1. Verifique se o arquivo existe
2. Configure manualmente nas opções
3. Nomes comuns: main.py, app.py, index.js

### Erro: "pkg não encontrado"
**Solução**: 
1. Instale Node.js: https://nodejs.org
2. Execute: `npm install -g pkg`

### Executável muito grande
**Soluções**:
- Para Python: Desmarque "arquivo único"
- Para Node.js: Marque "comprimir"
- Remova dependências desnecessárias

### Executável não inicia
**Verificações**:
1. Teste o projeto original primeiro
2. Verifique dependências externas
3. Veja logs para erros específicos

## 💡 Dicas e Truques

### **Para Projetos Python**

1. **Aplicações GUI**: Marque "Sem janela de console"
2. **Ícone personalizado**: Adicione caminho do .ico
3. **Arquivos extras**: Configure em "additional_files"
4. **Reduzir tamanho**: Use ambiente virtual limpo

### **Para Projetos Node.js**

1. **Multiplataforma**: Altere target conforme necessário
2. **Assets**: Copie manualmente arquivos estáticos
3. **Dependências nativas**: Podem causar problemas

### **Organização**

1. **Pasta de saída**: Use uma pasta dedicada
2. **Backup**: Mantenha código original seguro
3. **Teste**: Sempre teste o executável antes de distribuir

## 📊 Logs e Depuração

### Visualizar Logs
1. Vá para aba "Logs"
2. Ajuste nível de filtro (INFO, WARNING, ERROR)
3. Use "Limpar" para resetar
4. "Atualizar" para forçar refresh

### Logs Salvos
Os logs são salvos em:
- **Windows**: `%USERPROFILE%\.gerador_executavel\logs\`
- **Linux/Mac**: `~/.gerador_executavel/logs/`

## 🎨 Personalização

### Templates
1. Configure opções para um projeto
2. Salve como template (futuro)
3. Reutilize em projetos similares

### Configurações
Editáveis em: `~/.gerador_executavel/config.json`

## 🆘 Suporte

### Problemas Comuns
1. Consulte este guia primeiro
2. Verifique logs para detalhes
3. Teste com projetos de exemplo

### Reportar Bugs
1. Descreva o problema detalhadamente
2. Inclua logs relevantes
3. Mencione sistema operacional
4. Anexe projeto de teste (se possível)

### Recursos Adicionais
- 📖 README.md - Visão geral do projeto
- 📝 CHANGELOG.md - Histórico de mudanças
- 🧪 run_tests.py - Executar testes
- ⚙️ install.py - Reinstalar dependências

## 🚀 Próximos Passos

Após dominar o básico:

1. **Experimente** com diferentes tipos de projeto
2. **Personalize** configurações avançadas
3. **Automatize** builds frequentes
4. **Distribua** executáveis com confiança

---

**💡 Lembre-se**: O primeiro build pode demorar mais devido ao download de dependências. Builds subsequentes são mais rápidos!
