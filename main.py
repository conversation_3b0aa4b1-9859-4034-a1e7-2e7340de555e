#!/usr/bin/env python3
"""
Gerador de Executáveis - Aplicativo Principal
Aplicativo desktop para gerar executáveis de projetos em diferentes linguagens
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import sys
import threading
from pathlib import Path

# Adicionar o diretório src ao path para imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.gui.main_window import MainWindow
from src.core.config_manager import ConfigManager
from src.utils.logger import setup_logger

class ExecutableGeneratorApp:
    """Classe principal do aplicativo gerador de executáveis"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.config_manager = ConfigManager()
        self.logger = setup_logger()
        
        # Configurar janela principal
        self.setup_main_window()
        
        # Inicializar interface
        self.main_window = MainWindow(self.root, self.config_manager, self.logger)
        
    def setup_main_window(self):
        """Configurar propriedades da janela principal"""
        self.root.title("Gerador de Executáveis v1.0")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)
        
        # Centralizar janela
        self.center_window()
        
        # Configurar ícone (se existir)
        try:
            icon_path = os.path.join(os.path.dirname(__file__), 'assets', 'icon.ico')
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except:
            pass
            
    def center_window(self):
        """Centralizar janela na tela"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def run(self):
        """Executar aplicativo"""
        try:
            self.logger.info("Iniciando Gerador de Executáveis")
            self.root.mainloop()
        except Exception as e:
            self.logger.error(f"Erro ao executar aplicativo: {e}")
            messagebox.showerror("Erro", f"Erro inesperado: {e}")
        finally:
            self.logger.info("Aplicativo finalizado")

def main():
    """Função principal"""
    try:
        app = ExecutableGeneratorApp()
        app.run()
    except Exception as e:
        print(f"Erro crítico: {e}")
        messagebox.showerror("Erro Crítico", f"Não foi possível iniciar o aplicativo: {e}")

if __name__ == "__main__":
    main()
