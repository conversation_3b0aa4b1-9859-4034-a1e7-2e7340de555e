GERADOR DE EXECUTÁVEIS - INSTRUÇÕES
===================================

Executável gerado: main.exe
Tipo: Aplicação GUI (sem console)

PROBLEMAS COMUNS E SOLUÇÕES:
---------------------------

1. EXECUTÁVEL NÃO ABRE OU INTERFACE NÃO APARECE:
   - Execute o arquivo main_debug.exe (se disponível)
   - Esta versão mostra mensagens de erro no console
   - Verifique se há erros de dependências ou bibliotecas

2. ERRO DE DEPENDÊNCIAS:
   - Certifique-se que todas as bibliotecas estão instaladas
   - Execute: pip install -r requirements.txt (se existir)

3. PROBLEMAS COM TKINTER:
   - Verifique se o Python foi instalado com suporte a Tkinter
   - No Windows, reinstale o Python marcando "tcl/tk and IDLE"

4. ANTIVÍRUS BLOQUEANDO:
   - Alguns antivírus podem bloquear executáveis gerados pelo PyInstaller
   - Adicione o arquivo à lista de exceções do antivírus

5. EXECUTÁVEL MUITO LENTO PARA ABRIR:
   - É normal que executáveis PyInstaller demorem alguns segundos para abrir
   - Aguarde até 30 segundos na primeira execução

VERSÕES DISPONÍVEIS:
-------------------
- main.exe: Versão principal (GUI)
- main_debug.exe: Versão debug (com console)

Para suporte, execute a versão debug e anote as mensagens de erro.

Gerado em: E:\Nova pasta\Gerador Executavel
Data: N/A