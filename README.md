# Gerador de Executáveis

Um aplicativo desktop para gerar executáveis de projetos em diferentes linguagens de programação.

## 🚀 Funcionalidades

- **Suporte a múltiplas linguagens**: Python, Node.js, Java, .NET, Go, Rust
- **Interface gráfica intuitiva**: Desenvolvida com Tkinter
- **Detecção automática de projetos**: Identifica automaticamente o tipo de projeto
- **Configurações personalizáveis**: Templates e configurações por linguagem
- **Sistema de logs**: Acompanhe o progresso da geração
- **Multiplataforma**: Funciona no Windows, Linux e macOS

## 📋 Pré-requisitos

### Requisitos Básicos
- Python 3.8 ou superior
- pip (gerenciador de pacotes Python)

### Ferramentas por Linguagem

#### Python
- PyInstaller (instalado automaticamente)

#### Node.js
- Node.js e npm instalados
- pkg (instalado automaticamente quando necessário)

#### Java
- JDK 8 ou superior
- Maven ou Gradle

#### .NET
- .NET SDK 6.0 ou superior

#### Go
- Go 1.18 ou superior

#### Rust
- Rust e Cargo

## 🛠️ Instalação

1. Clone o repositório:
```bash
git clone <url-do-repositorio>
cd gerador-executavel
```

2. Instale as dependências:
```bash
pip install -r requirements.txt
```

3. Execute o aplicativo:
```bash
python main.py
```

## 📖 Como Usar

1. **Abra o aplicativo** executando `python main.py`
2. **Selecione o projeto** clicando em "Selecionar Projeto"
3. **Configure as opções** de build conforme necessário
4. **Clique em "Gerar Executável"** e aguarde o processo
5. **Encontre o executável** na pasta de saída especificada

## 🔧 Configurações

O aplicativo salva suas configurações em:
- **Windows**: `%USERPROFILE%\.gerador_executavel\`
- **Linux/macOS**: `~/.gerador_executavel/`

### Estrutura de Configuração
```
~/.gerador_executavel/
├── config.json          # Configurações principais
├── templates/           # Templates de build
└── logs/               # Arquivos de log
```

## 🏗️ Estrutura do Projeto

```
gerador-executavel/
├── main.py                    # Arquivo principal
├── requirements.txt           # Dependências
├── README.md                 # Este arquivo
├── src/
│   ├── core/                 # Módulos principais
│   │   ├── config_manager.py # Gerenciador de configurações
│   │   └── project_detector.py # Detector de projetos
│   ├── generators/           # Geradores por linguagem
│   │   ├── python_generator.py
│   │   ├── nodejs_generator.py
│   │   └── ...
│   ├── gui/                  # Interface gráfica
│   │   ├── main_window.py
│   │   └── components/
│   └── utils/                # Utilitários
│       ├── logger.py
│       └── file_utils.py
├── assets/                   # Recursos (ícones, etc.)
└── tests/                    # Testes unitários
```

## 🤝 Contribuindo

1. Faça um fork do projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📝 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.

## 🐛 Reportar Bugs

Se encontrar algum bug, por favor abra uma issue no GitHub com:
- Descrição detalhada do problema
- Passos para reproduzir
- Sistema operacional e versão do Python
- Logs relevantes (encontrados em `~/.gerador_executavel/logs/`)

## 📞 Suporte

Para suporte e dúvidas:
- Abra uma issue no GitHub
- Consulte a documentação
- Verifique os logs em caso de erro
