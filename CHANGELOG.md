# Changelog

Todas as mudanças notáveis neste projeto serão documentadas neste arquivo.

O formato é baseado em [Keep a Changelog](https://keepachangelog.com/pt-BR/1.0.0/),
e este projeto adere ao [Versionamento Semântico](https://semver.org/lang/pt-BR/).

## [1.0.0] - 2024-01-XX

### Adicionado
- Interface gráfica principal com Tkinter
- Suporte para projetos Python com PyInstaller
- Suporte para projetos Node.js com pkg
- Detector automático de tipos de projeto
- Sistema de configurações persistentes
- Sistema de templates de build
- Sistema de logging com interface visual
- Barra de progresso para acompanhar builds
- Testes unitários básicos
- Script de instalação automatizada
- Documentação completa

### Funcionalidades Principais
- **Detecção Automática**: Identifica automaticamente Python, Node.js, Java, .NET, Go, Rust e C/C++
- **Interface Intuitiva**: GUI moderna e fácil de usar
- **Configurações Flexíveis**: Opções específicas para cada linguagem
- **Templates**: Templates pré-configurados para diferentes tipos de projeto
- **Logs Detalhados**: Sistema completo de logging com visualização em tempo real
- **Multiplataforma**: Funciona no Windows, Linux e macOS

### Linguagens Suportadas
- ✅ **Python**: PyInstaller com opções completas
- ✅ **Node.js**: pkg com suporte a diferentes targets
- 🚧 **Java**: Detecção implementada, gerador em desenvolvimento
- 🚧 **.NET**: Detecção implementada, gerador em desenvolvimento
- 🚧 **Go**: Detecção implementada, gerador em desenvolvimento
- 🚧 **Rust**: Detecção implementada, gerador em desenvolvimento

### Estrutura do Projeto
```
gerador-executavel/
├── main.py                    # Arquivo principal
├── requirements.txt           # Dependências
├── install.py                # Script de instalação
├── run_tests.py              # Script de testes
├── src/
│   ├── core/                 # Módulos principais
│   ├── generators/           # Geradores por linguagem
│   ├── gui/                  # Interface gráfica
│   └── utils/                # Utilitários
├── tests/                    # Testes unitários
└── docs/                     # Documentação
```

### Dependências
- Python 3.8+
- PyInstaller (para projetos Python)
- Node.js + npm (para projetos Node.js)
- Outras ferramentas conforme necessário

## [Próximas Versões]

### Planejado para v1.1.0
- [ ] Gerador completo para Java (Maven/Gradle)
- [ ] Gerador completo para .NET
- [ ] Gerador completo para Go
- [ ] Gerador completo para Rust
- [ ] Interface para edição de templates
- [ ] Suporte a builds em lote
- [ ] Integração com Git

### Planejado para v1.2.0
- [ ] Suporte a C/C++ com CMake
- [ ] Plugin system para geradores customizados
- [ ] Interface web opcional
- [ ] Distribuição como executável único
- [ ] Suporte a Docker containers

### Ideias Futuras
- [ ] Integração com CI/CD
- [ ] Assinatura digital de executáveis
- [ ] Análise de dependências
- [ ] Otimização automática de tamanho
- [ ] Suporte a mais linguagens (Kotlin, Swift, etc.)

## Contribuindo

Contribuições são bem-vindas! Por favor:

1. Faça um fork do projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## Licença

Este projeto está licenciado sob a Licença MIT - veja o arquivo [LICENSE](LICENSE) para detalhes.
