"""
Factory de Geradores
Factory para criar geradores específicos por linguagem
"""

import logging
from typing import Dict, Optional
from .base_generator import BaseGenerator
from .python_generator import PythonGenerator
from .nodejs_generator import NodeJSGenerator

class GeneratorFactory:
    """Factory para criar geradores de executáveis"""
    
    # Mapeamento de tipos de projeto para classes de gerador
    GENERATORS = {
        'python': PythonGenerator,
        'nodejs': NodeJSGenerator,
        'node.js': NodeJSGenerator,
        # Adicionar outros geradores conforme implementados
        # 'java': JavaGenerator,
        # 'dotnet': DotNetGenerator,
        # 'go': GoGenerator,
        # 'rust': RustGenerator,
    }
    
    @classmethod
    def create_generator(cls, project_type: str, project_path: str, 
                        output_path: str, options: Dict, 
                        logger: logging.Logger) -> Optional[BaseGenerator]:
        """
        Criar gerador apropriado para o tipo de projeto
        
        Args:
            project_type: Tipo do projeto (python, nodejs, etc.)
            project_path: Caminho do projeto
            output_path: Caminho de saída
            options: Opções de build
            logger: Logger
            
        Returns:
            Instância do gerador ou None se não suportado
        """
        
        # Normalizar tipo do projeto
        normalized_type = project_type.lower().strip()
        
        # Remover informações extras (ex: "Java (Maven)" -> "java")
        if '(' in normalized_type:
            normalized_type = normalized_type.split('(')[0].strip()
            
        # Verificar se gerador existe
        generator_class = cls.GENERATORS.get(normalized_type)
        
        if generator_class:
            try:
                return generator_class(project_path, output_path, options, logger)
            except Exception as e:
                logger.error(f"Erro ao criar gerador para {project_type}: {e}")
                return None
        else:
            logger.warning(f"Gerador não implementado para tipo: {project_type}")
            return None
            
    @classmethod
    def get_supported_types(cls) -> list:
        """Obter lista de tipos de projeto suportados"""
        return list(cls.GENERATORS.keys())
        
    @classmethod
    def is_supported(cls, project_type: str) -> bool:
        """Verificar se tipo de projeto é suportado"""
        normalized_type = project_type.lower().strip()
        if '(' in normalized_type:
            normalized_type = normalized_type.split('(')[0].strip()
        return normalized_type in cls.GENERATORS
