"""
Gerenciador de Templates
Sistema para gerenciar templates de configuração de build
"""

import json
import yaml
from pathlib import Path
from typing import Dict, List, Any, Optional

class TemplateManager:
    """Gerenciador de templates de build"""
    
    def __init__(self, templates_dir: Path):
        self.templates_dir = templates_dir
        self.templates_dir.mkdir(exist_ok=True)
        
        # Criar templates padrão se não existirem
        self._create_default_templates()
        
    def _create_default_templates(self):
        """Criar templates padrão"""
        
        # Template Python
        python_template = {
            "name": "Python Padrão",
            "description": "Configuração padrão para projetos Python",
            "language": "python",
            "options": {
                "use_pyinstaller": True,
                "one_file": True,
                "no_console": False,
                "main_file": "main.py",
                "icon_path": "",
                "additional_args": [],
                "additional_files": ["README.md", "LICENSE", "config.json"]
            }
        }
        
        # Template Python GUI
        python_gui_template = {
            "name": "Python GUI",
            "description": "Configuração para aplicações GUI Python",
            "language": "python",
            "options": {
                "use_pyinstaller": True,
                "one_file": True,
                "no_console": False,  # Console habilitado para debug
                "main_file": "main.py",
                "icon_path": "icon.ico",
                "additional_args": [
                    "--hidden-import", "tkinter",
                    "--hidden-import", "tkinter.ttk",
                    "--hidden-import", "tkinter.messagebox",
                    "--add-data", "assets;assets"
                ],
                "additional_files": ["assets", "config"]
            }
        }

        # Template Python GUI (Sem Console)
        python_gui_windowed_template = {
            "name": "Python GUI (Sem Console)",
            "description": "Configuração para aplicações GUI Python sem janela de console",
            "language": "python",
            "options": {
                "use_pyinstaller": True,
                "one_file": True,
                "no_console": True,  # Sem console - para versão final
                "main_file": "main.py",
                "icon_path": "icon.ico",
                "additional_args": [
                    "--hidden-import", "tkinter",
                    "--hidden-import", "tkinter.ttk",
                    "--hidden-import", "tkinter.messagebox",
                    "--add-data", "assets;assets"
                ],
                "additional_files": ["assets", "config"]
            }
        }
        
        # Template Node.js
        nodejs_template = {
            "name": "Node.js Padrão",
            "description": "Configuração padrão para projetos Node.js",
            "language": "nodejs",
            "options": {
                "use_pkg": True,
                "target": "node18-win-x64",
                "compress": True,
                "main_file": "",
                "additional_files": ["public", "assets", "config.json"]
            }
        }
        
        # Template Node.js Web App
        nodejs_web_template = {
            "name": "Node.js Web App",
            "description": "Configuração para aplicações web Node.js",
            "language": "nodejs",
            "options": {
                "use_pkg": True,
                "target": "node18-win-x64",
                "compress": True,
                "main_file": "server.js",
                "additional_files": ["public", "views", "static", "uploads"]
            }
        }
        
        # Salvar templates
        templates = [
            python_template,
            python_gui_template,
            python_gui_windowed_template,
            nodejs_template,
            nodejs_web_template
        ]
        
        for template in templates:
            self.save_template(template)
            
    def save_template(self, template: Dict[str, Any]) -> bool:
        """
        Salvar template
        
        Args:
            template: Dados do template
            
        Returns:
            True se salvo com sucesso
        """
        
        try:
            # Gerar nome do arquivo baseado no nome do template
            filename = self._sanitize_filename(template['name']) + '.json'
            file_path = self.templates_dir / filename
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(template, f, indent=2, ensure_ascii=False)
                
            return True
            
        except Exception:
            return False
            
    def load_template(self, template_name: str) -> Optional[Dict[str, Any]]:
        """
        Carregar template por nome
        
        Args:
            template_name: Nome do template
            
        Returns:
            Dados do template ou None se não encontrado
        """
        
        filename = self._sanitize_filename(template_name) + '.json'
        file_path = self.templates_dir / filename
        
        if not file_path.exists():
            return None
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
                
        except Exception:
            return None
            
    def get_templates_by_language(self, language: str) -> List[Dict[str, Any]]:
        """
        Obter templates por linguagem
        
        Args:
            language: Linguagem (python, nodejs, etc.)
            
        Returns:
            Lista de templates
        """
        
        templates = []
        
        for template_file in self.templates_dir.glob('*.json'):
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    template = json.load(f)
                    
                if template.get('language', '').lower() == language.lower():
                    templates.append(template)
                    
            except Exception:
                continue
                
        return templates
        
    def get_all_templates(self) -> List[Dict[str, Any]]:
        """Obter todos os templates"""
        
        templates = []
        
        for template_file in self.templates_dir.glob('*.json'):
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    template = json.load(f)
                    templates.append(template)
                    
            except Exception:
                continue
                
        return templates
        
    def delete_template(self, template_name: str) -> bool:
        """
        Deletar template
        
        Args:
            template_name: Nome do template
            
        Returns:
            True se deletado com sucesso
        """
        
        filename = self._sanitize_filename(template_name) + '.json'
        file_path = self.templates_dir / filename
        
        try:
            if file_path.exists():
                file_path.unlink()
                return True
            return False
            
        except Exception:
            return False
            
    def create_template_from_options(self, name: str, description: str, 
                                   language: str, options: Dict[str, Any]) -> bool:
        """
        Criar template a partir de opções atuais
        
        Args:
            name: Nome do template
            description: Descrição
            language: Linguagem
            options: Opções de build
            
        Returns:
            True se criado com sucesso
        """
        
        template = {
            "name": name,
            "description": description,
            "language": language,
            "options": options.copy(),
            "created_at": self._get_current_timestamp()
        }
        
        return self.save_template(template)
        
    def export_template(self, template_name: str, export_path: Path) -> bool:
        """
        Exportar template para arquivo
        
        Args:
            template_name: Nome do template
            export_path: Caminho de exportação
            
        Returns:
            True se exportado com sucesso
        """
        
        template = self.load_template(template_name)
        if not template:
            return False
            
        try:
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(template, f, indent=2, ensure_ascii=False)
                
            return True
            
        except Exception:
            return False
            
    def import_template(self, import_path: Path) -> bool:
        """
        Importar template de arquivo
        
        Args:
            import_path: Caminho do arquivo
            
        Returns:
            True se importado com sucesso
        """
        
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                template = json.load(f)
                
            # Validar estrutura do template
            required_fields = ['name', 'language', 'options']
            if not all(field in template for field in required_fields):
                return False
                
            return self.save_template(template)
            
        except Exception:
            return False
            
    def _sanitize_filename(self, name: str) -> str:
        """Sanitizar nome para usar como nome de arquivo"""
        
        # Remover caracteres inválidos
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            name = name.replace(char, '_')
            
        # Limitar tamanho
        return name[:50].strip()
        
    def _get_current_timestamp(self) -> str:
        """Obter timestamp atual"""
        
        from datetime import datetime
        return datetime.now().isoformat()
        
    def validate_template(self, template: Dict[str, Any]) -> List[str]:
        """
        Validar template
        
        Args:
            template: Dados do template
            
        Returns:
            Lista de erros encontrados
        """
        
        errors = []
        
        # Verificar campos obrigatórios
        required_fields = ['name', 'language', 'options']
        for field in required_fields:
            if field not in template:
                errors.append(f"Campo obrigatório ausente: {field}")
                
        # Verificar tipos
        if 'name' in template and not isinstance(template['name'], str):
            errors.append("Nome deve ser uma string")
            
        if 'language' in template and not isinstance(template['language'], str):
            errors.append("Linguagem deve ser uma string")
            
        if 'options' in template and not isinstance(template['options'], dict):
            errors.append("Opções devem ser um dicionário")
            
        return errors
