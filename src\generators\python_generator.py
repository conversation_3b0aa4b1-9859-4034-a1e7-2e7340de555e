"""
Gerador Python
Gerador de executáveis para projetos Python usando PyInstaller
"""

import os
import shutil
from pathlib import Path
from typing import List, Dict, Any
from .base_generator import BaseGenerator

class PythonGenerator(BaseGenerator):
    """Gerador de executáveis para Python"""
    
    def __init__(self, project_path: str, output_path: str, options: Dict, logger):
        super().__init__(project_path, output_path, options, logger)
        
        # Opções específicas do Python
        self.use_pyinstaller = options.get('use_pyinstaller', True)
        self.one_file = options.get('one_file', True)
        self.no_console = options.get('no_console', False)
        self.main_file = options.get('main_file', 'main.py')
        self.icon_path = options.get('icon_path', '')
        self.additional_args = options.get('additional_args', [])
        
    def check_dependencies(self) -> List[str]:
        """Verificar dependências necessárias"""

        missing = []

        if self.use_pyinstaller:
            # Verificar se PyInstaller está instalado como módulo Python
            if not self._check_pyinstaller_available():
                missing.append('PyInstaller')

        # Verificar se Python está disponível
        if not self.check_command_exists('python'):
            missing.append('Python')

        return missing

    def _check_pyinstaller_available(self) -> bool:
        """Verificar se PyInstaller está disponível"""

        try:
            # Tentar importar PyInstaller
            import PyInstaller
            return True
        except ImportError:
            pass

        try:
            # Tentar executar como módulo
            result = self.run_command(['python', '-m', 'PyInstaller', '--version'])
            return result.returncode == 0
        except Exception:
            pass

        try:
            # Tentar comando direto
            result = self.run_command(['pyinstaller', '--version'])
            return result.returncode == 0
        except Exception:
            pass

        return False

    def generate(self) -> bool:
        """Gerar executável Python"""
        
        try:
            self.update_progress(0, "Iniciando geração do executável Python...")
            
            # Verificar dependências
            missing_deps = self.check_dependencies()
            if missing_deps:
                self.logger.error(f"Dependências faltando: {', '.join(missing_deps)}")
                return False
                
            self.update_progress(10, "Dependências verificadas")
            
            # Verificar arquivo principal
            main_file_path = self.project_path / self.main_file
            if not main_file_path.exists():
                # Tentar encontrar arquivo principal automaticamente
                main_file_path = self._find_main_file()
                if not main_file_path:
                    self.logger.error(f"Arquivo principal não encontrado: {self.main_file}")
                    return False
                    
            self.update_progress(20, f"Arquivo principal encontrado: {main_file_path.name}")
            
            # Instalar PyInstaller se necessário
            if self.use_pyinstaller and not self._check_pyinstaller_available():
                self.update_progress(25, "Instalando PyInstaller...")
                if not self._install_pyinstaller():
                    return False
                    
            self.update_progress(40, "PyInstaller pronto")
            
            # Gerar executável
            if self.use_pyinstaller:
                success = self._generate_with_pyinstaller(main_file_path)
            else:
                self.logger.error("Apenas PyInstaller é suportado atualmente")
                return False
                
            if success:
                self.update_progress(90, "Executável gerado, finalizando...")
                self._post_process()
                self.update_progress(100, "Executável Python gerado com sucesso!")
                return True
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"Erro ao gerar executável Python: {e}")
            return False
            
    def _find_main_file(self) -> Path:
        """Encontrar arquivo principal automaticamente"""
        
        # Lista de nomes comuns para arquivo principal
        main_candidates = ['main.py', 'app.py', '__main__.py', 'run.py', 'start.py']
        
        for candidate in main_candidates:
            candidate_path = self.project_path / candidate
            if candidate_path.exists():
                self.logger.info(f"Arquivo principal detectado automaticamente: {candidate}")
                return candidate_path
                
        # Procurar por arquivos Python na raiz
        python_files = list(self.project_path.glob('*.py'))
        if len(python_files) == 1:
            self.logger.info(f"Único arquivo Python encontrado: {python_files[0].name}")
            return python_files[0]
            
        return None
        
    def _install_pyinstaller(self) -> bool:
        """Instalar PyInstaller"""
        
        try:
            self.logger.info("Instalando PyInstaller...")
            result = self.run_command(['python', '-m', 'pip', 'install', 'pyinstaller'])
            
            if result.returncode == 0:
                self.logger.info("PyInstaller instalado com sucesso")
                return True
            else:
                self.logger.error(f"Falha ao instalar PyInstaller: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"Erro ao instalar PyInstaller: {e}")
            return False
            
    def _generate_with_pyinstaller(self, main_file: Path) -> bool:
        """Gerar executável usando PyInstaller"""

        try:
            # Construir comando PyInstaller (usar módulo Python)
            cmd = ['python', '-m', 'PyInstaller']

            # Opções básicas
            if self.one_file:
                cmd.append('--onefile')
            else:
                cmd.append('--onedir')

            # Configuração de console/windowed
            if self.no_console:
                cmd.append('--windowed')
                # Para aplicações GUI, adicionar flags para melhor compatibilidade
                cmd.extend(['--add-data', 'NUL;.'])  # Placeholder para dados
            else:
                cmd.append('--console')

            # Adicionar flags para melhor compatibilidade com Tkinter
            cmd.extend([
                '--hidden-import', 'tkinter',
                '--hidden-import', 'tkinter.ttk',
                '--hidden-import', 'tkinter.messagebox',
                '--hidden-import', 'tkinter.filedialog'
            ])

            # Ícone
            if self.icon_path and Path(self.icon_path).exists():
                cmd.extend(['--icon', self.icon_path])

            # Diretório de saída
            cmd.extend(['--distpath', str(self.output_path)])

            # Diretório de trabalho temporário
            work_path = self.output_path / 'build'
            cmd.extend(['--workpath', str(work_path)])

            # Arquivo spec temporário
            spec_path = self.output_path / 'temp.spec'
            cmd.extend(['--specpath', str(spec_path.parent)])

            # Argumentos adicionais
            if self.additional_args:
                cmd.extend(self.additional_args)

            # Arquivo principal
            cmd.append(str(main_file))

            self.update_progress(50, "Executando PyInstaller...")
            self.logger.info(f"Comando PyInstaller: {' '.join(cmd)}")

            # Executar PyInstaller
            result = self.run_command(cmd, cwd=self.project_path)

            if result.returncode == 0:
                self.logger.info("PyInstaller executado com sucesso")

                # Se for windowed, criar também uma versão debug
                if self.no_console:
                    self._create_debug_version(main_file)

                return True
            else:
                self.logger.error(f"PyInstaller falhou: {result.stderr}")
                return False

        except Exception as e:
            self.logger.error(f"Erro ao executar PyInstaller: {e}")
            return False

    def _create_debug_version(self, main_file: Path):
        """Criar versão debug com console para troubleshooting"""

        try:
            self.logger.info("Criando versão debug com console...")

            # Comando para versão debug
            cmd = ['python', '-m', 'PyInstaller']

            if self.one_file:
                cmd.append('--onefile')
            else:
                cmd.append('--onedir')

            # Forçar console para debug
            cmd.append('--console')

            # Adicionar flags para Tkinter
            cmd.extend([
                '--hidden-import', 'tkinter',
                '--hidden-import', 'tkinter.ttk',
                '--hidden-import', 'tkinter.messagebox',
                '--hidden-import', 'tkinter.filedialog'
            ])

            # Nome diferente para versão debug
            debug_name = f"{Path(self.main_file).stem}_debug"
            cmd.extend(['--name', debug_name])

            # Diretório de saída
            cmd.extend(['--distpath', str(self.output_path)])

            # Diretório de trabalho temporário
            work_path = self.output_path / 'build_debug'
            cmd.extend(['--workpath', str(work_path)])

            # Arquivo spec temporário
            spec_path = self.output_path / 'debug.spec'
            cmd.extend(['--specpath', str(spec_path.parent)])

            # Arquivo principal
            cmd.append(str(main_file))

            self.logger.info(f"Comando debug: {' '.join(cmd)}")

            # Executar PyInstaller para versão debug
            result = self.run_command(cmd, cwd=self.project_path)

            if result.returncode == 0:
                self.logger.info("Versão debug criada com sucesso")
            else:
                self.logger.warning(f"Falha ao criar versão debug: {result.stderr}")

        except Exception as e:
            self.logger.warning(f"Erro ao criar versão debug: {e}")

    def _post_process(self):
        """Pós-processamento após geração"""
        
        try:
            # Limpar arquivos temporários
            temp_dirs = ['build', '__pycache__']
            temp_files = ['*.spec']
            
            for temp_dir in temp_dirs:
                temp_path = self.output_path / temp_dir
                if temp_path.exists():
                    shutil.rmtree(temp_path)
                    self.logger.debug(f"Diretório temporário removido: {temp_path}")
                    
            # Remover arquivos spec temporários
            for spec_file in self.output_path.glob('*.spec'):
                spec_file.unlink()
                self.logger.debug(f"Arquivo spec removido: {spec_file}")
                
            # Copiar arquivos adicionais se especificado
            additional_files = self.options.get('additional_files', [])
            if additional_files:
                self.copy_additional_files(additional_files)

            # Criar arquivo de instruções se for windowed
            if self.no_console:
                self._create_instructions_file()

        except Exception as e:
            self.logger.warning(f"Erro no pós-processamento: {e}")

    def _create_instructions_file(self):
        """Criar arquivo de instruções para troubleshooting"""

        try:
            instructions_path = self.output_path / "LEIA-ME.txt"

            instructions = f"""
GERADOR DE EXECUTÁVEIS - INSTRUÇÕES
===================================

Executável gerado: {Path(self.main_file).stem}.exe
Tipo: Aplicação GUI (sem console)

PROBLEMAS COMUNS E SOLUÇÕES:
---------------------------

1. EXECUTÁVEL NÃO ABRE OU INTERFACE NÃO APARECE:
   - Execute o arquivo {Path(self.main_file).stem}_debug.exe (se disponível)
   - Esta versão mostra mensagens de erro no console
   - Verifique se há erros de dependências ou bibliotecas

2. ERRO DE DEPENDÊNCIAS:
   - Certifique-se que todas as bibliotecas estão instaladas
   - Execute: pip install -r requirements.txt (se existir)

3. PROBLEMAS COM TKINTER:
   - Verifique se o Python foi instalado com suporte a Tkinter
   - No Windows, reinstale o Python marcando "tcl/tk and IDLE"

4. ANTIVÍRUS BLOQUEANDO:
   - Alguns antivírus podem bloquear executáveis gerados pelo PyInstaller
   - Adicione o arquivo à lista de exceções do antivírus

5. EXECUTÁVEL MUITO LENTO PARA ABRIR:
   - É normal que executáveis PyInstaller demorem alguns segundos para abrir
   - Aguarde até 30 segundos na primeira execução

VERSÕES DISPONÍVEIS:
-------------------
- {Path(self.main_file).stem}.exe: Versão principal (GUI)
- {Path(self.main_file).stem}_debug.exe: Versão debug (com console)

Para suporte, execute a versão debug e anote as mensagens de erro.

Gerado em: {Path.cwd()}
Data: {Path(instructions_path).stat().st_mtime if instructions_path.exists() else 'N/A'}
"""

            with open(instructions_path, 'w', encoding='utf-8') as f:
                f.write(instructions.strip())

            self.logger.info(f"Arquivo de instruções criado: {instructions_path}")

        except Exception as e:
            self.logger.warning(f"Erro ao criar arquivo de instruções: {e}")
            
    def get_executable_info(self) -> Dict[str, Any]:
        """Obter informações do executável gerado"""
        
        info = {
            'type': 'Python',
            'generator': 'PyInstaller' if self.use_pyinstaller else 'Unknown',
            'one_file': self.one_file,
            'console': not self.no_console,
            'main_file': self.main_file
        }
        
        # Verificar se executável foi gerado
        if self.one_file:
            # Para onefile, o executável tem o nome do arquivo principal
            main_name = Path(self.main_file).stem  # Remove extensão
            exe_name = main_name
            if not exe_name.endswith('.exe') and os.name == 'nt':
                exe_name += '.exe'
            exe_path = self.output_path / exe_name

            if exe_path.exists():
                info['executable_path'] = str(exe_path)
                info['size_mb'] = round(exe_path.stat().st_size / (1024 * 1024), 2)
        else:
            # Para onedir, verificar pasta
            dist_path = self.output_path / self.get_project_name()
            if dist_path.exists():
                info['executable_dir'] = str(dist_path)
                
        return info
