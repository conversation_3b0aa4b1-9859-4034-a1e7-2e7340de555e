"""
Gerador Python
Gerador de executáveis para projetos Python usando PyInstaller
"""

import os
import shutil
from pathlib import Path
from typing import List, Dict, Any
from .base_generator import BaseGenerator

class PythonGenerator(BaseGenerator):
    """Gerador de executáveis para Python"""
    
    def __init__(self, project_path: str, output_path: str, options: Dict, logger):
        super().__init__(project_path, output_path, options, logger)
        
        # Opções específicas do Python
        self.use_pyinstaller = options.get('use_pyinstaller', True)
        self.one_file = options.get('one_file', True)
        self.no_console = options.get('no_console', False)
        self.main_file = options.get('main_file', 'main.py')
        self.icon_path = options.get('icon_path', '')
        self.additional_args = options.get('additional_args', [])
        
    def check_dependencies(self) -> List[str]:
        """Verificar dependências necessárias"""

        missing = []

        if self.use_pyinstaller:
            # Verificar se PyInstaller está instalado como módulo Python
            if not self._check_pyinstaller_available():
                missing.append('PyInstaller')

        # Verificar se Python está disponível
        if not self.check_command_exists('python'):
            missing.append('Python')

        return missing

    def _check_pyinstaller_available(self) -> bool:
        """Verificar se PyInstaller está disponível"""

        try:
            # Tentar importar PyInstaller
            import PyInstaller
            return True
        except ImportError:
            pass

        try:
            # Tentar executar como módulo
            result = self.run_command(['python', '-m', 'PyInstaller', '--version'])
            return result.returncode == 0
        except Exception:
            pass

        try:
            # Tentar comando direto
            result = self.run_command(['pyinstaller', '--version'])
            return result.returncode == 0
        except Exception:
            pass

        return False

    def generate(self) -> bool:
        """Gerar executável Python"""
        
        try:
            self.update_progress(0, "Iniciando geração do executável Python...")
            
            # Verificar dependências
            missing_deps = self.check_dependencies()
            if missing_deps:
                self.logger.error(f"Dependências faltando: {', '.join(missing_deps)}")
                return False
                
            self.update_progress(10, "Dependências verificadas")
            
            # Verificar arquivo principal
            main_file_path = self.project_path / self.main_file
            if not main_file_path.exists():
                # Tentar encontrar arquivo principal automaticamente
                main_file_path = self._find_main_file()
                if not main_file_path:
                    self.logger.error(f"Arquivo principal não encontrado: {self.main_file}")
                    return False
                    
            self.update_progress(20, f"Arquivo principal encontrado: {main_file_path.name}")
            
            # Instalar PyInstaller se necessário
            if self.use_pyinstaller and not self._check_pyinstaller_available():
                self.update_progress(25, "Instalando PyInstaller...")
                if not self._install_pyinstaller():
                    return False
                    
            self.update_progress(40, "PyInstaller pronto")
            
            # Gerar executável
            if self.use_pyinstaller:
                success = self._generate_with_pyinstaller(main_file_path)
            else:
                self.logger.error("Apenas PyInstaller é suportado atualmente")
                return False
                
            if success:
                self.update_progress(90, "Executável gerado, finalizando...")
                self._post_process()
                self.update_progress(100, "Executável Python gerado com sucesso!")
                return True
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"Erro ao gerar executável Python: {e}")
            return False
            
    def _find_main_file(self) -> Path:
        """Encontrar arquivo principal automaticamente"""
        
        # Lista de nomes comuns para arquivo principal
        main_candidates = ['main.py', 'app.py', '__main__.py', 'run.py', 'start.py']
        
        for candidate in main_candidates:
            candidate_path = self.project_path / candidate
            if candidate_path.exists():
                self.logger.info(f"Arquivo principal detectado automaticamente: {candidate}")
                return candidate_path
                
        # Procurar por arquivos Python na raiz
        python_files = list(self.project_path.glob('*.py'))
        if len(python_files) == 1:
            self.logger.info(f"Único arquivo Python encontrado: {python_files[0].name}")
            return python_files[0]
            
        return None
        
    def _install_pyinstaller(self) -> bool:
        """Instalar PyInstaller"""
        
        try:
            self.logger.info("Instalando PyInstaller...")
            result = self.run_command(['python', '-m', 'pip', 'install', 'pyinstaller'])
            
            if result.returncode == 0:
                self.logger.info("PyInstaller instalado com sucesso")
                return True
            else:
                self.logger.error(f"Falha ao instalar PyInstaller: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"Erro ao instalar PyInstaller: {e}")
            return False
            
    def _generate_with_pyinstaller(self, main_file: Path) -> bool:
        """Gerar executável usando PyInstaller"""
        
        try:
            # Construir comando PyInstaller (usar módulo Python)
            cmd = ['python', '-m', 'PyInstaller']
            
            # Opções básicas
            if self.one_file:
                cmd.append('--onefile')
            else:
                cmd.append('--onedir')
                
            if self.no_console:
                cmd.append('--windowed')
                
            # Ícone
            if self.icon_path and Path(self.icon_path).exists():
                cmd.extend(['--icon', self.icon_path])
                
            # Diretório de saída
            cmd.extend(['--distpath', str(self.output_path)])
            
            # Diretório de trabalho temporário
            work_path = self.output_path / 'build'
            cmd.extend(['--workpath', str(work_path)])
            
            # Arquivo spec temporário
            spec_path = self.output_path / 'temp.spec'
            cmd.extend(['--specpath', str(spec_path.parent)])
            
            # Argumentos adicionais
            if self.additional_args:
                cmd.extend(self.additional_args)
                
            # Arquivo principal
            cmd.append(str(main_file))
            
            self.update_progress(50, "Executando PyInstaller...")
            self.logger.info(f"Comando PyInstaller: {' '.join(cmd)}")
            
            # Executar PyInstaller
            result = self.run_command(cmd, cwd=self.project_path)
            
            if result.returncode == 0:
                self.logger.info("PyInstaller executado com sucesso")
                return True
            else:
                self.logger.error(f"PyInstaller falhou: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"Erro ao executar PyInstaller: {e}")
            return False
            
    def _post_process(self):
        """Pós-processamento após geração"""
        
        try:
            # Limpar arquivos temporários
            temp_dirs = ['build', '__pycache__']
            temp_files = ['*.spec']
            
            for temp_dir in temp_dirs:
                temp_path = self.output_path / temp_dir
                if temp_path.exists():
                    shutil.rmtree(temp_path)
                    self.logger.debug(f"Diretório temporário removido: {temp_path}")
                    
            # Remover arquivos spec temporários
            for spec_file in self.output_path.glob('*.spec'):
                spec_file.unlink()
                self.logger.debug(f"Arquivo spec removido: {spec_file}")
                
            # Copiar arquivos adicionais se especificado
            additional_files = self.options.get('additional_files', [])
            if additional_files:
                self.copy_additional_files(additional_files)
                
        except Exception as e:
            self.logger.warning(f"Erro no pós-processamento: {e}")
            
    def get_executable_info(self) -> Dict[str, Any]:
        """Obter informações do executável gerado"""
        
        info = {
            'type': 'Python',
            'generator': 'PyInstaller' if self.use_pyinstaller else 'Unknown',
            'one_file': self.one_file,
            'console': not self.no_console,
            'main_file': self.main_file
        }
        
        # Verificar se executável foi gerado
        if self.one_file:
            # Para onefile, o executável tem o nome do arquivo principal
            main_name = Path(self.main_file).stem  # Remove extensão
            exe_name = main_name
            if not exe_name.endswith('.exe') and os.name == 'nt':
                exe_name += '.exe'
            exe_path = self.output_path / exe_name

            if exe_path.exists():
                info['executable_path'] = str(exe_path)
                info['size_mb'] = round(exe_path.stat().st_size / (1024 * 1024), 2)
        else:
            # Para onedir, verificar pasta
            dist_path = self.output_path / self.get_project_name()
            if dist_path.exists():
                info['executable_dir'] = str(dist_path)
                
        return info
