"""
Componente Seletor de Projeto
Widget para seleção e detecção de projetos
"""

import tkinter as tk
from tkinter import ttk, filedialog
import os
from pathlib import Path
from typing import Callable, Optional

class ProjectSelector(ttk.LabelFrame):
    """Widget para seleção de projeto"""
    
    def __init__(self, parent, project_path_var: tk.StringVar, 
                 project_type_var: tk.StringVar, callback: Optional[Callable] = None):
        super().__init__(parent, text="Projeto", padding="5")
        
        self.project_path_var = project_path_var
        self.project_type_var = project_type_var
        self.callback = callback
        
        self.create_widgets()
        self.setup_layout()
        self.bind_events()
        
    def create_widgets(self):
        """Criar widgets do componente"""
        
        # Frame para caminho do projeto
        self.path_frame = ttk.Frame(self)
        
        self.path_label = ttk.Label(self.path_frame, text="Caminho:")
        
        self.path_entry = ttk.Entry(
            self.path_frame,
            textvariable=self.project_path_var,
            width=50
        )
        
        self.browse_btn = ttk.Button(
            self.path_frame,
            text="Procurar",
            command=self.browse_project
        )
        
        # Frame para informações do projeto
        self.info_frame = ttk.Frame(self)
        
        self.type_label = ttk.Label(self.info_frame, text="Tipo:")
        
        self.type_value = ttk.Label(
            self.info_frame,
            textvariable=self.project_type_var,
            foreground="blue"
        )
        
        self.detect_btn = ttk.Button(
            self.info_frame,
            text="Detectar",
            command=self.detect_project_type
        )
        
    def setup_layout(self):
        """Configurar layout"""
        
        # Configurar redimensionamento
        self.columnconfigure(0, weight=1)
        
        # Frame do caminho
        self.path_frame.grid(row=0, column=0, sticky="ew", pady=(0, 5))
        self.path_frame.columnconfigure(1, weight=1)
        
        self.path_label.grid(row=0, column=0, padx=(0, 5))
        self.path_entry.grid(row=0, column=1, sticky="ew", padx=(0, 5))
        self.browse_btn.grid(row=0, column=2)
        
        # Frame de informações
        self.info_frame.grid(row=1, column=0, sticky="ew")
        
        self.type_label.grid(row=0, column=0, padx=(0, 5))
        self.type_value.grid(row=0, column=1, padx=(0, 10))
        self.detect_btn.grid(row=0, column=2)
        
    def bind_events(self):
        """Vincular eventos"""
        
        # Detectar tipo quando caminho muda
        self.project_path_var.trace_add("write", self._on_path_changed)
        
    def browse_project(self):
        """Procurar projeto"""
        
        directory = filedialog.askdirectory(
            title="Selecionar Projeto",
            initialdir=self.project_path_var.get() or os.path.expanduser("~")
        )
        
        if directory:
            self.project_path_var.set(directory)
            
    def _on_path_changed(self, *args):
        """Callback quando caminho muda"""
        
        # Limpar tipo anterior
        self.project_type_var.set("")
        
        # Detectar automaticamente se caminho existe
        path = self.project_path_var.get()
        if path and os.path.exists(path):
            self.detect_project_type()
            
    def detect_project_type(self):
        """Detectar tipo do projeto"""
        
        path = self.project_path_var.get()
        if not path or not os.path.exists(path):
            self.project_type_var.set("Caminho inválido")
            return
            
        project_path = Path(path)
        detected_type = self._detect_project_type(project_path)
        
        self.project_type_var.set(detected_type)
        
        # Chamar callback se fornecido
        if self.callback:
            self.callback(path, detected_type)
            
    def _detect_project_type(self, project_path: Path) -> str:
        """
        Detectar tipo do projeto baseado nos arquivos presentes
        
        Args:
            project_path: Caminho do projeto
            
        Returns:
            Tipo do projeto detectado
        """
        
        # Verificar arquivos característicos
        files = [f.name for f in project_path.iterdir() if f.is_file()]
        
        # Python
        if any(f in files for f in ['main.py', 'app.py', '__main__.py', 'setup.py', 'pyproject.toml']):
            return "Python"
        if 'requirements.txt' in files or 'Pipfile' in files:
            return "Python"
        if any(f.endswith('.py') for f in files):
            return "Python"
            
        # Node.js
        if 'package.json' in files:
            return "Node.js"
        if 'yarn.lock' in files or 'package-lock.json' in files:
            return "Node.js"
            
        # Java
        if 'pom.xml' in files:
            return "Java (Maven)"
        if 'build.gradle' in files or 'build.gradle.kts' in files:
            return "Java (Gradle)"
        if any(f.endswith('.java') for f in files):
            return "Java"
            
        # .NET
        if any(f.endswith('.csproj') for f in files):
            return ".NET"
        if any(f.endswith('.sln') for f in files):
            return ".NET"
        if any(f.endswith('.cs') for f in files):
            return ".NET"
            
        # Go
        if 'go.mod' in files:
            return "Go"
        if any(f.endswith('.go') for f in files):
            return "Go"
            
        # Rust
        if 'Cargo.toml' in files:
            return "Rust"
        if any(f.endswith('.rs') for f in files):
            return "Rust"
            
        # C/C++
        if 'CMakeLists.txt' in files or 'Makefile' in files:
            return "C/C++"
        if any(f.endswith(('.c', '.cpp', '.cxx', '.cc')) for f in files):
            return "C/C++"
            
        # Verificar subdiretórios para projetos mais complexos
        subdirs = [d.name for d in project_path.iterdir() if d.is_dir()]
        
        if 'src' in subdirs:
            # Verificar dentro de src
            src_path = project_path / 'src'
            if src_path.exists():
                src_files = [f.name for f in src_path.iterdir() if f.is_file()]
                
                if any(f.endswith('.py') for f in src_files):
                    return "Python"
                if any(f.endswith('.java') for f in src_files):
                    return "Java"
                if any(f.endswith('.cs') for f in src_files):
                    return ".NET"
                if any(f.endswith('.go') for f in src_files):
                    return "Go"
                if any(f.endswith('.rs') for f in src_files):
                    return "Rust"
                    
        return "Desconhecido"
