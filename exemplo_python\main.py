#!/usr/bin/env python3
"""
Exemplo de Aplicativo Python
Aplicativo simples para testar o Gerador de Executáveis
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

class ExemploApp:
    """Aplicativo de exemplo"""
    
    def __init__(self):
        print("Criando janela principal...")
        self.root = tk.Tk()
        self.root.title("Exemplo Python - Gerador de Executáveis")
        self.root.geometry("400x300")

        print("Configurando janela...")
        # Garantir que a janela apareça na frente
        self.root.lift()
        self.root.attributes('-topmost', True)
        self.root.after_idle(self.root.attributes, '-topmost', False)

        # Configurar comportamento de fechamento
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Centralizar janela
        self.center_window()

        print("Criando widgets...")
        self.create_widgets()
        print("Aplicativo inicializado")

    def center_window(self):
        """Centralizar janela na tela"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_widgets(self):
        """Criar widgets da interface"""
        
        # Título
        title_label = tk.Label(
            self.root,
            text="🚀 Exemplo Python",
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=20)
        
        # Descrição
        desc_label = tk.Label(
            self.root,
            text="Este é um aplicativo de exemplo\npara testar o Gerador de Executáveis",
            justify="center"
        )
        desc_label.pack(pady=10)
        
        # Informações do sistema
        info_frame = tk.Frame(self.root)
        info_frame.pack(pady=20)
        
        tk.Label(info_frame, text=f"Python: {sys.version_info.major}.{sys.version_info.minor}").pack()
        tk.Label(info_frame, text=f"Sistema: {os.name}").pack()
        tk.Label(info_frame, text=f"Diretório: {os.getcwd()}").pack()
        
        # Botões
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=20)
        
        hello_btn = tk.Button(
            button_frame,
            text="Dizer Olá",
            command=self.say_hello,
            bg="#4CAF50",
            fg="white",
            padx=20
        )
        hello_btn.pack(side="left", padx=10)
        
        info_btn = tk.Button(
            button_frame,
            text="Informações",
            command=self.show_info,
            bg="#2196F3",
            fg="white",
            padx=20
        )
        info_btn.pack(side="left", padx=10)
        
        quit_btn = tk.Button(
            button_frame,
            text="Sair",
            command=self.root.quit,
            bg="#f44336",
            fg="white",
            padx=20
        )
        quit_btn.pack(side="left", padx=10)
        
    def say_hello(self):
        """Mostrar mensagem de olá"""
        messagebox.showinfo("Olá!", "Olá! Este executável foi gerado com sucesso! 🎉")
        
    def show_info(self):
        """Mostrar informações do aplicativo"""
        info = f"""
Aplicativo de Exemplo Python

Versão: 1.0.0
Python: {sys.version}
Executável: {sys.executable}
Argumentos: {sys.argv}

Este aplicativo foi criado para testar o
Gerador de Executáveis.
        """
        messagebox.showinfo("Informações", info)
        
    def run(self):
        """Executar aplicativo"""
        print("Iniciando interface gráfica...")

        # Garantir que a janela seja visível
        self.root.deiconify()  # Mostrar janela se estiver minimizada
        self.root.focus_force()  # Forçar foco

        try:
            self.root.mainloop()
        except Exception as e:
            print(f"Erro na interface: {e}")
            input("Pressione Enter para continuar...")

    def on_closing(self):
        """Callback ao fechar janela"""
        print("Fechando aplicativo...")
        self.root.destroy()

def main():
    """Função principal"""
    print("Iniciando aplicativo de exemplo...")

    try:
        # Verificar se Tkinter está disponível
        import tkinter as tk
        print("Tkinter disponível")

        # Testar criação de janela simples
        test_root = tk.Tk()
        test_root.withdraw()  # Esconder janela de teste
        print("Janela de teste criada")
        test_root.destroy()

        # Criar aplicativo principal
        app = ExemploApp()
        print("Aplicativo criado")

        app.run()

    except ImportError as e:
        print(f"Erro: Tkinter não disponível: {e}")
        input("Pressione Enter para sair...")
    except Exception as e:
        print(f"Erro inesperado: {e}")
        import traceback
        traceback.print_exc()
        input("Pressione Enter para sair...")

    print("Aplicativo finalizado.")

if __name__ == "__main__":
    main()
