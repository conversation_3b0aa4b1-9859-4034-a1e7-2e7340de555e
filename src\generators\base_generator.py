"""
Gerador Base
Classe base para todos os geradores de executáveis
"""

import os
import subprocess
import logging
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Dict, List, Optional, Callable
import shutil

class BaseGenerator(ABC):
    """Classe base para geradores de executáveis"""
    
    def __init__(self, project_path: str, output_path: str, options: Dict, logger: logging.Logger):
        self.project_path = Path(project_path)
        self.output_path = Path(output_path)
        self.options = options
        self.logger = logger
        self.progress_callback: Optional[Callable] = None
        
        # Validar caminhos
        if not self.project_path.exists():
            raise ValueError(f"Caminho do projeto não existe: {project_path}")
            
        # Criar diretório de saída se não existir
        self.output_path.mkdir(parents=True, exist_ok=True)
        
    def set_progress_callback(self, callback: Callable[[int, str], None]):
        """Definir callback para progresso"""
        self.progress_callback = callback
        
    def update_progress(self, percentage: int, message: str = ""):
        """Atualizar progresso"""
        if self.progress_callback:
            self.progress_callback(percentage, message)
        self.logger.info(f"Progresso: {percentage}% - {message}")
        
    @abstractmethod
    def generate(self) -> bool:
        """
        Gerar executável
        
        Returns:
            True se sucesso, False caso contrário
        """
        pass
        
    @abstractmethod
    def check_dependencies(self) -> List[str]:
        """
        Verificar dependências necessárias
        
        Returns:
            Lista de dependências faltantes
        """
        pass
        
    def run_command(self, command: List[str], cwd: Optional[Path] = None, 
                   capture_output: bool = True) -> subprocess.CompletedProcess:
        """
        Executar comando do sistema
        
        Args:
            command: Lista com comando e argumentos
            cwd: Diretório de trabalho
            capture_output: Se deve capturar saída
            
        Returns:
            Resultado do comando
        """
        
        if cwd is None:
            cwd = self.project_path
            
        self.logger.debug(f"Executando comando: {' '.join(command)} em {cwd}")
        
        try:
            result = subprocess.run(
                command,
                cwd=cwd,
                capture_output=capture_output,
                text=True,
                check=False
            )
            
            if result.returncode != 0:
                self.logger.error(f"Comando falhou: {result.stderr}")
            else:
                self.logger.debug(f"Comando executado com sucesso: {result.stdout}")
                
            return result
            
        except Exception as e:
            self.logger.error(f"Erro ao executar comando: {e}")
            raise
            
    def check_command_exists(self, command: str) -> bool:
        """
        Verificar se comando existe no sistema
        
        Args:
            command: Nome do comando
            
        Returns:
            True se comando existe
        """
        
        return shutil.which(command) is not None
        
    def copy_additional_files(self, files: List[str]):
        """
        Copiar arquivos adicionais para o diretório de saída
        
        Args:
            files: Lista de arquivos para copiar
        """
        
        for file_path in files:
            src = self.project_path / file_path
            if src.exists():
                dst = self.output_path / file_path
                dst.parent.mkdir(parents=True, exist_ok=True)
                
                if src.is_file():
                    shutil.copy2(src, dst)
                    self.logger.debug(f"Arquivo copiado: {src} -> {dst}")
                elif src.is_dir():
                    shutil.copytree(src, dst, dirs_exist_ok=True)
                    self.logger.debug(f"Diretório copiado: {src} -> {dst}")
                    
    def clean_build_artifacts(self, patterns: List[str]):
        """
        Limpar artefatos de build
        
        Args:
            patterns: Lista de padrões de arquivos para remover
        """
        
        import glob
        
        for pattern in patterns:
            for file_path in glob.glob(str(self.project_path / pattern)):
                path = Path(file_path)
                try:
                    if path.is_file():
                        path.unlink()
                        self.logger.debug(f"Arquivo removido: {path}")
                    elif path.is_dir():
                        shutil.rmtree(path)
                        self.logger.debug(f"Diretório removido: {path}")
                except Exception as e:
                    self.logger.warning(f"Erro ao remover {path}: {e}")
                    
    def get_project_name(self) -> str:
        """Obter nome do projeto"""
        return self.project_path.name
        
    def get_output_executable_path(self, name: Optional[str] = None) -> Path:
        """
        Obter caminho do executável de saída
        
        Args:
            name: Nome do executável (opcional)
            
        Returns:
            Caminho do executável
        """
        
        if name is None:
            name = self.get_project_name()
            
        # Adicionar extensão .exe no Windows
        if os.name == 'nt' and not name.endswith('.exe'):
            name += '.exe'
            
        return self.output_path / name
