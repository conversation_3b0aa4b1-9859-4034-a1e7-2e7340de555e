#!/usr/bin/env node
/**
 * Exemplo de Aplicativo Node.js
 * Aplicativo simples para testar o Gerador de Executáveis
 */

const readline = require('readline-sync');
const os = require('os');
const path = require('path');

class ExemploApp {
    constructor() {
        this.name = 'Exemplo Node.js';
        this.version = '1.0.0';
    }

    showHeader() {
        console.log('='.repeat(50));
        console.log(`🚀 ${this.name} v${this.version}`);
        console.log('Aplicativo de exemplo para testar o Gerador de Executáveis');
        console.log('='.repeat(50));
        console.log();
    }

    showSystemInfo() {
        console.log('📋 Informações do Sistema:');
        console.log(`   Node.js: ${process.version}`);
        console.log(`   Plataforma: ${os.platform()} ${os.arch()}`);
        console.log(`   Sistema: ${os.type()} ${os.release()}`);
        console.log(`   Diretório: ${process.cwd()}`);
        console.log(`   Executável: ${process.execPath}`);
        console.log();
    }

    showMenu() {
        console.log('📋 Menu de Opções:');
        console.log('1. Dizer Olá');
        console.log('2. Mostrar informações detalhadas');
        console.log('3. Calcular números');
        console.log('4. Sair');
        console.log();
    }

    sayHello() {
        const name = readline.question('Digite seu nome: ');
        console.log(`\n🎉 Olá, ${name}! Este executável foi gerado com sucesso!\n`);
    }

    showDetailedInfo() {
        console.log('\n📊 Informações Detalhadas:');
        console.log(`   Processo ID: ${process.pid}`);
        console.log(`   Memória usada: ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)} MB`);
        console.log(`   Tempo ativo: ${Math.round(process.uptime())} segundos`);
        console.log(`   Argumentos: ${process.argv.join(' ')}`);
        console.log(`   Variáveis de ambiente: ${Object.keys(process.env).length} variáveis`);
        console.log();
    }

    calculate() {
        console.log('\n🔢 Calculadora Simples:');
        
        try {
            const num1 = parseFloat(readline.question('Digite o primeiro número: '));
            const operator = readline.question('Digite a operação (+, -, *, /): ');
            const num2 = parseFloat(readline.question('Digite o segundo número: '));

            let result;
            switch (operator) {
                case '+':
                    result = num1 + num2;
                    break;
                case '-':
                    result = num1 - num2;
                    break;
                case '*':
                    result = num1 * num2;
                    break;
                case '/':
                    if (num2 === 0) {
                        console.log('❌ Erro: Divisão por zero!');
                        return;
                    }
                    result = num1 / num2;
                    break;
                default:
                    console.log('❌ Operação inválida!');
                    return;
            }

            console.log(`✅ Resultado: ${num1} ${operator} ${num2} = ${result}\n`);
        } catch (error) {
            console.log('❌ Erro nos números digitados!\n');
        }
    }

    run() {
        this.showHeader();
        this.showSystemInfo();

        while (true) {
            this.showMenu();
            const choice = readline.question('Escolha uma opção (1-4): ');

            switch (choice) {
                case '1':
                    this.sayHello();
                    break;
                case '2':
                    this.showDetailedInfo();
                    break;
                case '3':
                    this.calculate();
                    break;
                case '4':
                    console.log('👋 Obrigado por usar o aplicativo de exemplo!');
                    console.log('Aplicativo finalizado.\n');
                    return;
                default:
                    console.log('❌ Opção inválida! Tente novamente.\n');
            }
        }
    }
}

// Função principal
function main() {
    console.log('Iniciando aplicativo de exemplo Node.js...\n');
    
    const app = new ExemploApp();
    app.run();
}

// Executar se for o arquivo principal
if (require.main === module) {
    main();
}

module.exports = ExemploApp;
