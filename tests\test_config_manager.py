"""
Testes para o Gerenciador de Configurações
"""

import unittest
import tempfile
import shutil
from pathlib import Path
import json

# Adicionar src ao path
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from core.config_manager import ConfigManager

class TestConfigManager(unittest.TestCase):
    """Testes para ConfigManager"""
    
    def setUp(self):
        """Configurar testes"""
        self.temp_dir = Path(tempfile.mkdtemp())
        
        # Criar ConfigManager com diretório temporário
        self.config_manager = ConfigManager()
        self.config_manager.config_dir = self.temp_dir
        self.config_manager.config_file = self.temp_dir / 'config.json'
        
    def tearDown(self):
        """Limpar após testes"""
        shutil.rmtree(self.temp_dir)
        
    def test_default_config_creation(self):
        """Testar criação de configuração padrão"""
        
        config = self.config_manager.load_config()
        
        self.assertIsInstance(config, dict)
        self.assertIn('language_settings', config)
        self.assertIn('ui_settings', config)
        self.assertIn('build_settings', config)
        
    def test_get_config_value(self):
        """Testar obtenção de valor de configuração"""
        
        # Testar valor existente
        value = self.config_manager.get('ui_settings.theme')
        self.assertEqual(value, 'default')
        
        # Testar valor inexistente com padrão
        value = self.config_manager.get('nonexistent.key', 'default_value')
        self.assertEqual(value, 'default_value')
        
    def test_set_config_value(self):
        """Testar definição de valor de configuração"""
        
        # Definir novo valor
        self.config_manager.set('test.key', 'test_value')
        
        # Verificar se foi salvo
        value = self.config_manager.get('test.key')
        self.assertEqual(value, 'test_value')
        
    def test_language_config(self):
        """Testar configurações específicas de linguagem"""
        
        # Obter configuração Python
        python_config = self.config_manager.get_language_config('python')
        self.assertIsInstance(python_config, dict)
        self.assertIn('use_pyinstaller', python_config)
        
        # Definir nova configuração
        new_config = {'custom_option': True}
        self.config_manager.set_language_config('test_lang', new_config)
        
        # Verificar se foi salva
        saved_config = self.config_manager.get_language_config('test_lang')
        self.assertEqual(saved_config, new_config)
        
    def test_config_persistence(self):
        """Testar persistência de configurações"""
        
        # Definir valor
        self.config_manager.set('persistent.test', 'persistent_value')
        
        # Criar novo ConfigManager
        new_manager = ConfigManager()
        new_manager.config_dir = self.temp_dir
        new_manager.config_file = self.temp_dir / 'config.json'
        new_config = new_manager.load_config()
        
        # Verificar se valor foi persistido
        self.assertEqual(new_config['persistent']['test'], 'persistent_value')
        
    def test_reset_to_defaults(self):
        """Testar reset para configurações padrão"""
        
        # Modificar configuração
        self.config_manager.set('test.modified', True)
        
        # Reset
        self.config_manager.reset_to_defaults()
        
        # Verificar se voltou ao padrão
        value = self.config_manager.get('test.modified')
        self.assertIsNone(value)

if __name__ == '__main__':
    unittest.main()
