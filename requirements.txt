# Dependências do Gerador de Executáveis

# Interface gráfica (já incluído no Python padrão)
# tkinter

# Utilitários para empacotamento
PyInstaller>=5.13.0
auto-py-to-exe>=2.40.0

# Detecção de projetos e arquivos
pathspec>=0.11.0
gitignore-parser>=0.1.0

# Logging e configuração
colorlog>=6.7.0
pyyaml>=6.0

# Utilitários de sistema
psutil>=5.9.0
send2trash>=1.8.0

# Para projetos Node.js
# pkg será instalado via npm quando necessário

# Para projetos Java
# Maven e Gradle devem estar instalados no sistema

# Para projetos .NET
# dotnet CLI deve estar instalado no sistema

# Para projetos Go
# Go deve estar instalado no sistema

# Para projetos Rust
# Cargo deve estar instalado no sistema

# Para parsing de arquivos TOML (Rust)
toml>=0.10.0
