('E:\\Gerador '
 'Executavel\\exemplo_python\\output_test_windowed\\build\\main\\PYZ-00.pyz',
 [('__future__',
   'C:\\Program Files\\Python313\\Lib\\__future__.py',
   'PYMODULE'),
  ('_colorize', 'C:\\Program Files\\Python313\\Lib\\_colorize.py', 'PYMODULE'),
  ('_compat_pickle',
   'C:\\Program Files\\Python313\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Program Files\\Python313\\Lib\\_compression.py',
   'PYMODULE'),
  ('_opcode_metadata',
   'C:\\Program Files\\Python313\\Lib\\_opcode_metadata.py',
   'PYMODULE'),
  ('_py_abc', 'C:\\Program Files\\Python313\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime',
   'C:\\Program Files\\Python313\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Program Files\\Python313\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_strptime', 'C:\\Program Files\\Python313\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Program Files\\Python313\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'C:\\Program Files\\Python313\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\Program Files\\Python313\\Lib\\ast.py', 'PYMODULE'),
  ('base64', 'C:\\Program Files\\Python313\\Lib\\base64.py', 'PYMODULE'),
  ('bisect', 'C:\\Program Files\\Python313\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'C:\\Program Files\\Python313\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'C:\\Program Files\\Python313\\Lib\\calendar.py', 'PYMODULE'),
  ('contextlib',
   'C:\\Program Files\\Python313\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Program Files\\Python313\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'C:\\Program Files\\Python313\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'C:\\Program Files\\Python313\\Lib\\csv.py', 'PYMODULE'),
  ('dataclasses',
   'C:\\Program Files\\Python313\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'C:\\Program Files\\Python313\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'C:\\Program Files\\Python313\\Lib\\decimal.py', 'PYMODULE'),
  ('dis', 'C:\\Program Files\\Python313\\Lib\\dis.py', 'PYMODULE'),
  ('email',
   'C:\\Program Files\\Python313\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Program Files\\Python313\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Program Files\\Python313\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Program Files\\Python313\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Program Files\\Python313\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Program Files\\Python313\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Program Files\\Python313\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Program Files\\Python313\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Program Files\\Python313\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Program Files\\Python313\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Program Files\\Python313\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Program Files\\Python313\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Program Files\\Python313\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Program Files\\Python313\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Program Files\\Python313\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Program Files\\Python313\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Program Files\\Python313\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Program Files\\Python313\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Program Files\\Python313\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Program Files\\Python313\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch', 'C:\\Program Files\\Python313\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'C:\\Program Files\\Python313\\Lib\\fractions.py', 'PYMODULE'),
  ('getopt', 'C:\\Program Files\\Python313\\Lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'C:\\Program Files\\Python313\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'C:\\Program Files\\Python313\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'C:\\Program Files\\Python313\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'C:\\Program Files\\Python313\\Lib\\hashlib.py', 'PYMODULE'),
  ('importlib',
   'C:\\Program Files\\Python313\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Program Files\\Python313\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Program Files\\Python313\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Program Files\\Python313\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Program Files\\Python313\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Program Files\\Python313\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Program Files\\Python313\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Program Files\\Python313\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Program Files\\Python313\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Program Files\\Python313\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Program Files\\Python313\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'C:\\Program Files\\Python313\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Program Files\\Python313\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Program Files\\Python313\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Program Files\\Python313\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Program Files\\Python313\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'C:\\Program Files\\Python313\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Program Files\\Python313\\Lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'C:\\Program Files\\Python313\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'C:\\Program Files\\Python313\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Program Files\\Python313\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Program Files\\Python313\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Program Files\\Python313\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'C:\\Program Files\\Python313\\Lib\\lzma.py', 'PYMODULE'),
  ('numbers', 'C:\\Program Files\\Python313\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'C:\\Program Files\\Python313\\Lib\\opcode.py', 'PYMODULE'),
  ('pathlib',
   'C:\\Program Files\\Python313\\Lib\\pathlib\\__init__.py',
   'PYMODULE'),
  ('pathlib._abc',
   'C:\\Program Files\\Python313\\Lib\\pathlib\\_abc.py',
   'PYMODULE'),
  ('pathlib._local',
   'C:\\Program Files\\Python313\\Lib\\pathlib\\_local.py',
   'PYMODULE'),
  ('pickle', 'C:\\Program Files\\Python313\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'C:\\Program Files\\Python313\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile',
   'C:\\Program Files\\Python313\\Lib\\py_compile.py',
   'PYMODULE'),
  ('quopri', 'C:\\Program Files\\Python313\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Program Files\\Python313\\Lib\\random.py', 'PYMODULE'),
  ('selectors', 'C:\\Program Files\\Python313\\Lib\\selectors.py', 'PYMODULE'),
  ('shutil', 'C:\\Program Files\\Python313\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Program Files\\Python313\\Lib\\signal.py', 'PYMODULE'),
  ('socket', 'C:\\Program Files\\Python313\\Lib\\socket.py', 'PYMODULE'),
  ('statistics',
   'C:\\Program Files\\Python313\\Lib\\statistics.py',
   'PYMODULE'),
  ('string', 'C:\\Program Files\\Python313\\Lib\\string.py', 'PYMODULE'),
  ('stringprep',
   'C:\\Program Files\\Python313\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Program Files\\Python313\\Lib\\subprocess.py',
   'PYMODULE'),
  ('tarfile', 'C:\\Program Files\\Python313\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\Program Files\\Python313\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\Program Files\\Python313\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Program Files\\Python313\\Lib\\threading.py', 'PYMODULE'),
  ('tkinter',
   'C:\\Program Files\\Python313\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Program Files\\Python313\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Program Files\\Python313\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Program Files\\Python313\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('token', 'C:\\Program Files\\Python313\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Program Files\\Python313\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc',
   'C:\\Program Files\\Python313\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('typing', 'C:\\Program Files\\Python313\\Lib\\typing.py', 'PYMODULE'),
  ('urllib',
   'C:\\Program Files\\Python313\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Program Files\\Python313\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Program Files\\Python313\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Program Files\\Python313\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Program Files\\Python313\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE')])
