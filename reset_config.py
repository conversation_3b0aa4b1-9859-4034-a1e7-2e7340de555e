import sys
sys.path.append('.')
from src.core.config_manager import ConfigManager
import os

config = ConfigManager()
if config.config_file.exists():
    os.remove(config.config_file)
    print('Arquivo de configuração removido')

config.reset_to_defaults()
print('Configurações resetadas para padrão')

# Verificar a nova configuração
options = config.get_language_config('python')
print('no_console:', options.get('no_console', False))