#!/usr/bin/env python3
"""
Teste rápido da interface gráfica
"""

import sys
import os
from pathlib import Path

# Adicionar src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Testar imports dos componentes"""
    
    try:
        print("🧪 Testando imports...")
        
        from core.config_manager import ConfigManager
        print("✅ ConfigManager importado")
        
        from gui.components.settings_panel import SettingsPanel
        print("✅ SettingsPanel importado")
        
        from gui.main_window import MainWindow
        print("✅ MainWindow importado")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro no import: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_manager():
    """Testar ConfigManager"""
    
    try:
        print("\n🧪 Testando ConfigManager...")
        
        config = ConfigManager()
        
        # Testar get/set
        config.set('test.value', 'test123')
        value = config.get('test.value')
        
        if value == 'test123':
            print("✅ ConfigManager funcionando")
            return True
        else:
            print("❌ ConfigManager não salvou/carregou corretamente")
            return False
            
    except Exception as e:
        print(f"❌ Erro no ConfigManager: {e}")
        return False

def test_settings_panel():
    """Testar SettingsPanel"""
    
    try:
        print("\n🧪 Testando SettingsPanel...")
        
        import tkinter as tk
        from core.config_manager import ConfigManager
        from gui.components.settings_panel import SettingsPanel
        
        # Criar janela de teste
        root = tk.Tk()
        root.withdraw()  # Esconder janela
        
        config = ConfigManager()
        panel = SettingsPanel(root, config)
        
        print("✅ SettingsPanel criado com sucesso")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Erro no SettingsPanel: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Função principal"""
    
    print("🚀 Teste da Interface Gráfica")
    print("=" * 40)
    
    tests = [
        test_imports,
        test_config_manager,
        test_settings_panel
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
            
    print("\n" + "=" * 40)
    print(f"📊 Resultado: {passed}/{total} testes passaram")
    
    if passed == total:
        print("🎉 Todos os testes passaram!")
        return 0
    else:
        print("❌ Alguns testes falharam!")
        return 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
